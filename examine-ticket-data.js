#!/usr/bin/env node

/**
 * Examine ticket data structure sent to AI
 */

import axios from 'axios';

const API_BASE = 'http://localhost:4000';

async function examineTicketData() {
  try {
    console.log('🔍 Fetching Service Desk tickets to examine data structure...');
    const ticketsResponse = await axios.get(`${API_BASE}/api/tickets`, {
      params: {
        timeRange: '1month',
        server: 'servicedesk',
        maxResults: 3
      }
    });
    
    const tickets = ticketsResponse.data.tickets;
    console.log(`✅ Fetched ${tickets.length} tickets`);
    
    console.log('\n📊 SAMPLE TICKET DATA STRUCTURE:');
    console.log('=' + '='.repeat(80));
    console.log(JSON.stringify(tickets[0], null, 2));
    console.log('=' + '='.repeat(80));
    
    console.log('\n🔍 TICKET DATA FIELDS ANALYSIS:');
    const sampleTicket = tickets[0];
    Object.keys(sampleTicket).forEach(key => {
      const value = sampleTicket[key];
      const type = typeof value;
      let preview;
      if (type === 'string') {
        preview = value.length > 50 ? value.substring(0, 50) + '...' : value;
      } else {
        preview = JSON.stringify(value);
        if (preview.length > 50) {
          preview = preview.substring(0, 50) + '...';
        }
      }
      console.log(`  ${key}: ${type} - ${preview}`);
    });
    
    console.log('\n📝 WHAT AI RECEIVES (simplified format):');
    const ticketSummaries = tickets.map(ticket => ({
      key: ticket.key,
      summary: ticket.summary,
      status: ticket.status,
      assignee: ticket.assignee,
      epic: ticket.epic ? `${ticket.epic.key}: ${ticket.epic.summary}` : 'No Epic',
      timeSpent: ticket.timeSpentSeconds ? `${Math.round(ticket.timeSpentSeconds / 3600)}h` : '0h',
      source: ticket.sourceServer,
      created: ticket.created?.split('T')[0],
      updated: ticket.updated?.split('T')[0]
    }));
    
    console.log(JSON.stringify(ticketSummaries, null, 2));
    
    console.log('\n🎯 DATA QUALITY ASSESSMENT:');
    console.log(`📊 Tickets with descriptions: ${tickets.filter(t => t.description && t.description.length > 10).length}/${tickets.length}`);
    console.log(`📊 Tickets with assignees: ${tickets.filter(t => t.assignee && t.assignee !== 'Unassigned').length}/${tickets.length}`);
    console.log(`📊 Tickets with time logged: ${tickets.filter(t => t.timeSpentSeconds > 0).length}/${tickets.length}`);
    console.log(`📊 Tickets with epics: ${tickets.filter(t => t.epic).length}/${tickets.length}`);
    
    // Check for patterns in summaries
    console.log('\n🔍 SUMMARY PATTERNS:');
    const summaries = tickets.map(t => t.summary);
    const commonWords = {};
    summaries.forEach(summary => {
      const words = summary.toLowerCase().split(/\s+/);
      words.forEach(word => {
        if (word.length > 3) {
          commonWords[word] = (commonWords[word] || 0) + 1;
        }
      });
    });
    
    const sortedWords = Object.entries(commonWords)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);
    
    console.log('Top recurring words in summaries:');
    sortedWords.forEach(([word, count]) => {
      console.log(`  ${word}: ${count} times`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

examineTicketData();
