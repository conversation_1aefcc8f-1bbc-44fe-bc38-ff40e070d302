# Technical Documentation - <PERSON><PERSON> Helper

## 🏗️ Architecture Overview

Jira Helper is a full-stack web application built with modern technologies, designed for scalability, maintainability, and performance. The architecture follows a clean separation of concerns with a RESTful API backend and a responsive React frontend.

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  Express Backend │    │   External APIs  │
│                 │    │                 │    │                 │
│  • Components   │◄──►│  • REST API     │◄──►│  • Valmet Jira  │
│  • State Mgmt   │    │  • Data Processing│   │  • Service Desk │
│  • UI/UX        │    │  • AI Integration│   │  • LM Studio    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Technology Stack

### Backend Technologies

#### Core Framework
- **Node.js 18+**: JavaScript runtime environment
- **Express.js 4.18+**: Web application framework
- **ES Modules**: Modern JavaScript module system

#### HTTP Client & APIs
- **Axios 1.6+**: Promise-based HTTP client for API requests
- **CORS**: Cross-Origin Resource Sharing middleware
- **dotenv**: Environment variable management

#### Data Processing
- **xml2js**: XML parsing for Jira activity feeds
- **Custom JQL builders**: Dynamic Jira Query Language generation
- **Data transformation pipelines**: Ticket normalization and enrichment

#### AI Integration
- **LM Studio Integration**: Local AI model communication
- **OpenAI-compatible API**: Standardized AI request/response format
- **Custom prompt engineering**: Delivery-focused AI analysis

### Frontend Technologies

#### Core Framework
- **React 18+**: Component-based UI library
- **Vite 5+**: Fast build tool and development server
- **JavaScript ES6+**: Modern JavaScript features

#### Styling & UI
- **TailwindCSS 3+**: Utility-first CSS framework
- **Responsive Design**: Mobile-first approach
- **Component Libraries**: Custom reusable components

#### State Management
- **React Hooks**: useState, useEffect for local state
- **Context API**: Global state management where needed
- **Custom Hooks**: Reusable stateful logic

#### HTTP Client
- **Fetch API**: Native browser HTTP client
- **Error Handling**: Comprehensive error boundaries
- **Loading States**: User feedback during async operations

## 🔧 Backend Architecture

### Server Structure

```javascript
backend/
├── server.js              # Main server file
├── package.json           # Dependencies and scripts
└── tests/                 # Test files
    ├── backend.test.js    # Unit tests
    └── integration/       # Integration tests
```

### Core Components

#### 1. Jira Integration Layer
```javascript
// Dual Jira instance support
const jiraAxios = createJiraClient(JIRA_BASE, JIRA_PAT);
const jiraSDAxios = createJiraClient(JIRASD_BASE, JIRASD_PAT);

// Dynamic JQL query building
function buildJQL(options) {
  const { timeRange, assignee, project, team } = options;
  // Intelligent query construction based on parameters
}
```

#### 2. Data Processing Pipeline
```javascript
// Ticket normalization and enrichment
async function processTickets(rawTickets) {
  // 1. Normalize ticket data structure
  // 2. Fetch epic information
  // 3. Calculate time metrics
  // 4. Enrich with metadata
}
```

#### 3. Team Activity Engine
```javascript
// Dynamic activity fetching with intelligent sizing
async function fetchDynamicActivityFeed(memberName, dateFilter, timeRange) {
  // 1. Calculate optimal fetch size
  // 2. Parse XML activity feeds
  // 3. Filter by timeframe
  // 4. Analyze activity patterns
}
```

#### 4. AI Analysis System
```javascript
// Specialized AI prompts for different scenarios
const prompts = {
  deliveryAnalysis: "ROLE: Delivery lead assistant...",
  teamPerformance: "ROLE: Team performance analyzer...",
  serviceDesk: "ROLE: Infrastructure issue analyzer..."
};
```

### API Endpoints

#### Core Data Endpoints
- `GET /api/tickets` - Fetch and analyze tickets
- `GET /api/team-activity` - Team member activity tracking
- `GET /api/config` - Server configuration and available projects

#### AI Analysis Endpoints
- `POST /api/analyze` - Ticket analysis with AI
- `POST /api/analyze-team` - Team performance analysis

#### Utility Endpoints
- `GET /api/health` - Server health check
- `GET /api/ai-health` - LM Studio connectivity check

### Data Flow

```
1. Request → Validation → JQL Building
2. Jira API Calls → Data Normalization → Enrichment
3. Activity Feed Parsing → Pattern Analysis
4. AI Processing → Response Formatting
5. Caching → Response Delivery
```

## 🎨 Frontend Architecture

### Component Structure

```
frontend/src/
├── App.jsx                    # Main application component
├── components/
│   ├── TicketTable.jsx       # Data table component
│   ├── WorklogChart.jsx      # Visualization component
│   ├── TeamActivityDashboard.jsx  # Team tracking
│   ├── EnhancedPresetButtons.jsx  # Preset controls
│   └── CustomAnalysisSection.jsx  # Custom queries
├── index.css                 # Global styles
└── main.jsx                  # Application entry point
```

### Component Design Patterns

#### 1. Container Components
```javascript
// Main application state management
function App() {
  const [tickets, setTickets] = useState([]);
  const [teamActivities, setTeamActivities] = useState([]);
  const [config, setConfig] = useState({});
  // Centralized state and API calls
}
```

#### 2. Presentation Components
```javascript
// Pure components for data display
function TicketTable({ tickets, stats }) {
  // Render ticket data with sorting and filtering
}
```

#### 3. Smart Components
```javascript
// Components with their own state and logic
function TeamActivityDashboard() {
  const [loading, setLoading] = useState(false);
  const [aiAnalysis, setAiAnalysis] = useState('');
  // Self-contained functionality
}
```

### State Management Strategy

#### Local State (useState)
- Component-specific data
- UI state (loading, expanded sections)
- Form inputs and validation

#### Lifted State
- Shared data between components
- API responses and cached data
- Global configuration

#### Custom Hooks (Future Enhancement)
```javascript
// Reusable stateful logic
function useJiraData(options) {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  // Custom data fetching logic
}
```

## 🔄 Data Processing

### Jira Data Transformation

#### 1. Ticket Normalization
```javascript
function normalizeTicket(jiraTicket, source) {
  return {
    key: jiraTicket.key,
    summary: jiraTicket.fields.summary,
    status: jiraTicket.fields.status.name,
    assignee: jiraTicket.fields.assignee?.displayName,
    timeSpentSeconds: jiraTicket.fields.timespent || 0,
    epic: extractEpicInfo(jiraTicket),
    source: source // 'primary' or 'servicedesk'
  };
}
```

#### 2. Epic Enhancement
```javascript
async function enhanceWithEpics(tickets) {
  const epicKeys = extractUniqueEpicKeys(tickets);
  const epicDetails = await fetchEpicDetails(epicKeys);
  return tickets.map(ticket => ({
    ...ticket,
    epic: epicDetails[ticket.epicKey] || null
  }));
}
```

#### 3. Activity Feed Processing
```javascript
async function parseActivityFeed(xmlData, memberName, dateFilter) {
  const result = await parseStringPromise(xmlData);
  return result.feed.entry.map(entry => ({
    published: entry.published[0],
    title: cleanHtmlContent(entry.title[0]),
    activityType: categorizeActivity(entry.title[0]),
    ticketKey: extractTicketKey(entry.title[0])
  }));
}
```

### Statistical Analysis

#### Team Performance Metrics
```javascript
function calculateTeamStats(teamActivities) {
  return {
    totalTickets: sum(activities.map(a => a.stats.totalTickets)),
    totalTimeSpent: sum(activities.map(a => a.stats.totalTimeSpent)),
    avgProductivity: average(activities.map(a => a.stats.avgTicketsPerDay)),
    collaborationIndex: calculateCollaboration(activities)
  };
}
```

#### Activity Pattern Analysis
```javascript
function analyzeActivityPatterns(activities) {
  return {
    byType: groupBy(activities, 'activityType'),
    perDay: groupBy(activities, activity => activity.published.split('T')[0]),
    mostActiveDay: findMostActiveDay(activities),
    collaborationPatterns: analyzeCollaboration(activities)
  };
}
```

## 🤖 AI Integration

### LM Studio Integration

#### Connection Management
```javascript
const LLM_CONFIG = {
  endpoint: process.env.LLM_ENDPOINT,
  model: process.env.LLM_MODEL,
  timeout: parseInt(process.env.LLM_TIMEOUT) || 60000
};

async function callLMStudio(messages, options = {}) {
  const response = await axios.post(LLM_CONFIG.endpoint, {
    model: LLM_CONFIG.model,
    messages,
    temperature: options.temperature || 0.2,
    max_tokens: options.maxTokens || 2000
  });
  return response.data.choices[0].message.content;
}
```

#### Prompt Engineering

##### Delivery Analysis Prompt
```javascript
const DELIVERY_PROMPT = `
ROLE: You are a delivery lead assistant analyzing Jira tickets for client discussions.

CONTEXT:
- Team: Cloud architects working on Valmet Industrial Internet DevOps
- Purpose: Prepare insights for client delivery meetings
- Audience: Technical stakeholders and project managers

ANALYSIS PRIORITIES:
1. DELIVERY INSIGHTS: Progress on key initiatives and milestones
2. TEAM PRODUCTIVITY: Work distribution and capacity utilization
3. TECHNICAL PATTERNS: Common issues and architectural decisions
4. CLIENT VALUE: Demonstrable outcomes and business impact
`;
```

##### Team Performance Prompt
```javascript
const TEAM_PROMPT = `
ROLE: You are a delivery lead assistant analyzing team performance.

ANALYSIS PRIORITIES:
1. REAL ACTIVITY ANALYSIS: Daily activities from Jira activity feeds
2. TEAM PRODUCTIVITY ASSESSMENT: Individual performance patterns
3. COLLABORATION & SPECIALIZATION: Cross-team collaboration patterns
4. MANAGEMENT RECOMMENDATIONS: Workload rebalancing suggestions
`;
```

### AI Response Processing

#### Chunked Analysis for Large Datasets
```javascript
async function summarizeInChunks(tickets, options, config) {
  const chunkSize = 50; // Optimal size for LM Studio processing
  const totalTickets = tickets.length;
  const totalChunks = Math.ceil(totalTickets / chunkSize);

  console.log(`🔄 Processing ${totalTickets} tickets in ${totalChunks} chunks`);

  const chunkSummaries = [];

  // Process each chunk individually with error handling
  for (let i = 0; i < totalChunks; i++) {
    const chunkTickets = tickets.slice(i * chunkSize, (i + 1) * chunkSize);

    try {
      const chunkOptions = { ...options, chunk: true, chunkIndex: i };
      const chunkSummary = await summarizeTickets(chunkTickets, chunkOptions, config);
      chunkSummaries.push({ chunkIndex: i + 1, summary: chunkSummary });

      // Delay between chunks to be nice to LM Studio
      if (i < totalChunks - 1) await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (error) {
      console.error(`❌ Error processing chunk ${i + 1}:`, error.message);
      chunkSummaries.push({ chunkIndex: i + 1, summary: `Error: ${error.message}`, error: true });
    }
  }

  // Combine all chunk summaries using hierarchical summarization
  const combinedText = chunkSummaries.map(chunk =>
    `## Chunk ${chunk.chunkIndex}\n${chunk.summary}`
  ).join('\n\n');

  const finalOptions = { ...options, summaryMode: true, textToSummarize: combinedText };
  return await summarizeTickets([], finalOptions, config);
}
```

**Key Features:**
- **Processes ALL tickets**: No longer limited to first 50 tickets
- **Hierarchical summarization**: Combines chunk summaries into comprehensive final analysis
- **Error resilience**: Individual chunk failures don't stop the entire process
- **LM Studio optimization**: 2-second delays between chunks prevent overloading
- **Progress tracking**: Detailed logging for monitoring large dataset processing

## 🔒 Security & Performance

### Security Measures

#### Environment Variable Protection
```javascript
// Sensitive data in environment variables
const JIRA_PAT = process.env.JIRA_PAT;
const JIRASD_PAT = process.env.JIRASD_PAT;

// No credentials in client-side code
// API endpoints handle authentication
```

#### CORS Configuration
```javascript
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));
```

#### Input Validation
```javascript
function validateTicketRequest(req, res, next) {
  const { timeRange, assignee, project } = req.query;
  // Validate and sanitize inputs
  next();
}
```

### Performance Optimizations

#### Caching Strategy
```javascript
// In-memory caching for frequently accessed data
const cache = new Map();

function getCachedData(key, ttl = 300000) { // 5 minutes
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data;
  }
  return null;
}
```

#### Efficient Data Fetching
```javascript
// Parallel API calls for better performance
const [primaryTickets, serviceDeskTickets] = await Promise.all([
  fetchTicketsFromJira(jiraAxios, primaryOptions),
  jiraSDAxios ? fetchTicketsFromJira(jiraSDAxios, serviceDeskOptions) : []
]);
```

#### Dynamic Activity Fetching
```javascript
// Intelligent sizing based on activity levels
function shouldContinueFetching(allActivities, activitiesInTimeframe, maxResults, daysDiff) {
  // Smart logic to determine optimal fetch size
  const expectedMinActivities = Math.max(5, Math.floor(daysDiff / 7));
  return activitiesInTimeframe.length < expectedMinActivities && 
         allActivities.length === maxResults;
}
```

## 📊 Monitoring & Debugging

### Logging Strategy
```javascript
// Structured logging for debugging
console.log(`🔍 Fetching team activity for: ${teamMembers.join(', ')}`);
console.log(`📊 Found ${activities.length} real activity entries`);
console.log(`✅ Team analysis completed (${analysis.length} characters)`);
```

### Health Checks
```javascript
// API health monitoring
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    jira: jiraAxios ? 'configured' : 'not configured',
    ai: LLM_ENDPOINT ? 'configured' : 'not configured'
  });
});
```

### Error Handling
```javascript
// Comprehensive error handling
try {
  const result = await processRequest();
  res.json(result);
} catch (error) {
  console.error('❌ Request failed:', error.message);
  res.status(500).json({
    error: 'Processing failed',
    details: error.message,
    timestamp: new Date().toISOString()
  });
}
```

## 🚀 Deployment Considerations

### Environment Configuration
- **Development**: Local Jira instances, LM Studio
- **Production**: Secure credential management, load balancing
- **Testing**: Mock services, automated testing

### Scalability
- **Horizontal scaling**: Stateless API design
- **Caching layers**: Redis for production environments
- **Database integration**: Future enhancement for data persistence

### Monitoring
- **Application metrics**: Response times, error rates
- **Business metrics**: Analysis frequency, user engagement
- **Infrastructure metrics**: Memory usage, CPU utilization
