#!/usr/bin/env node

/**
 * Test the chunking fix - verify all tickets are processed
 */

import axios from 'axios';

const API_BASE = 'http://localhost:4000';

console.log('🧪 Testing Chunking Fix - Verify All Tickets Are Processed\n');

async function testChunkingFix() {
  try {
    console.log('1️⃣ Fetching Service Desk tickets...');
    
    // Fetch a larger dataset to trigger chunking
    const ticketsResponse = await axios.get(`${API_BASE}/api/tickets`, {
      params: {
        timeRange: '1month',
        server: 'servicedesk',
        maxResults: 100
      }
    });
    
    const totalTickets = ticketsResponse.data.tickets.length;
    console.log(`   ✅ Fetched ${totalTickets} tickets`);
    
    if (totalTickets < 50) {
      console.log('   ⚠️  Not enough tickets to test chunking (need >50 for chunking to trigger)');
      console.log('   💡 Try increasing timeRange or maxResults to get more tickets');
      return;
    }
    
    console.log('\n2️⃣ Running AI Analysis with chunking...');
    console.log('   📊 This should process ALL tickets, not just the first 50');
    
    const startTime = Date.now();
    
    const analysisResponse = await axios.post(`${API_BASE}/api/analyze`, {
      tickets: ticketsResponse.data.tickets,
      options: {
        scenario: 'Service Desk Alert Analysis - Chunking Test'
      }
    }, {
      timeout: 300000 // 5 minutes timeout for chunking
    });
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    const analysis = analysisResponse.data.summary;
    const processedTickets = analysisResponse.data.ticketCount;
    
    console.log(`   ✅ Analysis completed in ${duration} seconds`);
    console.log(`   📊 Tickets processed: ${processedTickets}/${totalTickets}`);
    console.log(`   📝 Analysis length: ${analysis.length} characters`);
    
    // Verify all tickets were processed
    if (processedTickets === totalTickets) {
      console.log('\n🎉 SUCCESS: All tickets were processed!');
      console.log('   ✅ Chunking fix is working correctly');
    } else {
      console.log('\n❌ ISSUE: Not all tickets were processed');
      console.log(`   Expected: ${totalTickets}, Got: ${processedTickets}`);
      console.log('   🔍 Check the chunking logic in backend/services/ai/analysis.js');
    }
    
    // Show a sample of the analysis
    console.log('\n📄 Analysis Sample (first 500 characters):');
    console.log(`"${analysis.substring(0, 500)}..."`);
    
    // Check if analysis mentions chunks
    if (analysis.toLowerCase().includes('chunk')) {
      console.log('\n🔗 Analysis mentions chunks - hierarchical summarization working');
    }
    
    return {
      totalTickets,
      processedTickets,
      analysis,
      duration,
      success: processedTickets === totalTickets
    };
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.code === 'ECONNABORTED') {
      console.log('   ⏰ Timeout - chunking may be taking longer than expected');
      console.log('   💡 This is normal for large datasets with LM Studio');
    } else if (error.response?.status === 500) {
      console.log('   🔧 Server error - check backend logs for details');
    }
    
    return { success: false, error: error.message };
  }
}

async function main() {
  console.log('🚀 Starting chunking fix test...\n');
  
  // Check if server is running
  try {
    await axios.get(`${API_BASE}/api/health`);
    console.log('✅ Server is running\n');
  } catch (error) {
    console.log('❌ Server is not running. Please start the backend server first.');
    process.exit(1);
  }
  
  const result = await testChunkingFix();
  
  if (result.success) {
    console.log('\n🎯 CHUNKING FIX VERIFIED');
    console.log('   All tickets are now being processed correctly!');
    console.log('   Your monthly service desk analysis will include all data.');
  } else {
    console.log('\n⚠️  CHUNKING FIX NEEDS ATTENTION');
    console.log('   Some tickets may still be missing from analysis.');
  }
}

main().catch(console.error);
