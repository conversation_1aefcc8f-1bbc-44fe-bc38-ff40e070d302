import { useState, useEffect } from 'react';

// Reusable component for clickable ticket links
const TicketLink = ({ ticketKey, ticketUrl, className = "" }) => {
  if (!ticketKey) return null;

  // If we have a direct URL from the activity feed, use it
  if (ticketUrl) {
    return (
      <a
        href={ticketUrl}
        target="_blank"
        rel="noopener noreferrer"
        className={`hover:underline cursor-pointer transition-colors ${className}`}
        title={`Open ${ticketKey} in Jira`}
      >
        {ticketKey}
      </a>
    );
  }

  // Fallback: construct URL (activities are from primary Jira)
  const fallbackUrl = `https://jira.aut.valmet.com/browse/${ticketKey}`;
  return (
    <a
      href={fallbackUrl}
      target="_blank"
      rel="noopener noreferrer"
      className={`hover:underline cursor-pointer transition-colors ${className}`}
      title={`Open ${ticketKey} in Jira`}
    >
      {ticketKey}
    </a>
  );
};

export default function TeamActivityDashboard() {
  const [teamActivities, setTeamActivities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [analyzingAI, setAnalyzingAI] = useState(false);
  const [error, setError] = useState('');
  const [timeRange, setTimeRange] = useState('2weeks');
  const [selectedMember, setSelectedMember] = useState('');
  const [lastRefresh, setLastRefresh] = useState(null);
  const [teamMembers, setTeamMembers] = useState([]);
  const [aiAnalysis, setAiAnalysis] = useState('');
  const [lastAIAnalysis, setLastAIAnalysis] = useState(null);
  const [fetchingSummary, setFetchingSummary] = useState(null);
  const [expandedActivityFeeds, setExpandedActivityFeeds] = useState({});

  const fetchTeamActivity = async () => {
    setLoading(true);
    setError('');
    setAiAnalysis(''); // Clear previous AI analysis

    const params = new URLSearchParams();
    params.append('timeRange', timeRange);
    if (selectedMember) params.append('member', selectedMember);

    try {
      const response = await fetch(`http://localhost:4000/api/team-activity?${params}`);
      const data = await response.json();

      if (response.ok) {
        setTeamActivities(data.teamActivities);
        setTeamMembers(data.teamMembers);
        setFetchingSummary(data.fetchingSummary);
        setLastRefresh(new Date(data.timestamp));
      } else {
        setError(data.error || 'Failed to load team activity');
      }
    } catch (err) {
      setError('Failed to connect to server');
    } finally {
      setLoading(false);
    }
  };

  const analyzeTeamWithAI = async () => {
    if (teamActivities.length === 0) {
      setError('No team activity data to analyze. Please fetch team activity first.');
      return;
    }

    setAnalyzingAI(true);
    setError('');

    try {
      const response = await fetch('http://localhost:4000/api/analyze-team', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          teamActivities,
          options: { timeRange, selectedMember }
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setAiAnalysis(data.summary);
        setLastAIAnalysis(new Date(data.timestamp));
      } else {
        setError(data.error || 'Failed to analyze team performance');
      }
    } catch (err) {
      setError('Failed to connect to server for AI analysis');
    } finally {
      setAnalyzingAI(false);
    }
  };

  useEffect(() => {
    fetchTeamActivity();
  }, [timeRange, selectedMember]);

  // Listen for auto-refresh events from the main app
  useEffect(() => {
    const handleAutoRefresh = () => {
      fetchTeamActivity();
    };

    window.addEventListener('refreshTeamActivity', handleAutoRefresh);

    return () => {
      window.removeEventListener('refreshTeamActivity', handleAutoRefresh);
    };
  }, [timeRange, selectedMember]);

  const formatTime = (seconds) => {
    if (!seconds) return '0h';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffMinutes < 60) {
      return `${diffMinutes}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const formatExactTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const isToday = (dateString) => {
    const date = new Date(dateString);
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const getTodaysActivities = () => {
    const todaysActivities = [];

    teamActivities.forEach(memberData => {
      if (!memberData.error && memberData.realActivityFeed) {
        memberData.realActivityFeed.forEach(activity => {
          if (isToday(activity.published)) {
            todaysActivities.push({
              ...activity,
              memberName: memberData.member,
              memberDisplayName: memberData.member
            });
          }
        });
      }
    });

    // Sort by timestamp, most recent first
    return todaysActivities.sort((a, b) => new Date(b.published) - new Date(a.published));
  };

  const toggleActivityFeed = (memberIndex) => {
    setExpandedActivityFeeds(prev => ({
      ...prev,
      [memberIndex]: !prev[memberIndex]
    }));
  };

  const getTotalTeamStats = () => {
    return teamActivities.reduce((total, member) => {
      if (!member.error) {
        total.tickets += member.stats.totalTickets;
        total.timeSpent += member.stats.totalTimeSpent;
      }
      return total;
    }, { tickets: 0, timeSpent: 0 });
  };

  const totalStats = getTotalTeamStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Team Activity Dashboard</h2>
        <div className="flex items-center space-x-4">
          {lastRefresh && (
            <div className="text-sm text-gray-600">
              Last refresh: {lastRefresh.toLocaleString()}
            </div>
          )}
          <button
            onClick={fetchTeamActivity}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            disabled={loading}
          >
            {loading ? '🔄 Loading...' : '🔄 Refresh'}
          </button>
          {teamActivities.length > 0 && (
            <button
              onClick={analyzeTeamWithAI}
              className={`px-4 py-2 rounded text-white ${
                analyzingAI ? 'bg-gray-400' : 'bg-purple-600 hover:bg-purple-700'
              }`}
              disabled={analyzingAI}
            >
              {analyzingAI ? '🤖 Analyzing Team...' : '🤖 AI Team Analysis'}
            </button>
          )}
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex flex-wrap gap-4 items-center">
          <div>
            <label className="block text-sm font-medium mb-1">Time Range</label>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="1week">1 Week</option>
              <option value="2weeks">2 Weeks</option>
              <option value="1month">1 Month</option>
              <option value="2months">2 Months</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Team Member</label>
            <select
              value={selectedMember}
              onChange={(e) => setSelectedMember(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="">All Team Members</option>
              {teamMembers.map(member => (
                <option key={member} value={member}>{member}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-semibold mb-2">Error Loading Team Activity</h3>
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* Today's Combined Activity Timeline */}
      {teamActivities.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-lg shadow-lg p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h3 className="text-xl font-bold text-blue-900 flex items-center">
                🕐 Today's Combined Activity Timeline
              </h3>
              <p className="text-blue-700 text-sm mt-1">
                Real-time chronological view of all team activities for {new Date().toLocaleDateString()}
              </p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-600">
                {getTodaysActivities().length}
              </div>
              <div className="text-sm text-blue-600">activities today</div>
            </div>
          </div>

          {getTodaysActivities().length > 0 ? (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {getTodaysActivities().map((activity, idx) => (
                <div key={`${activity.memberName}-${idx}`} className="bg-white rounded-lg border border-blue-200 p-4 shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-start space-x-4">
                    {/* Time and Member */}
                    <div className="flex-shrink-0 text-center">
                      <div className="text-lg font-bold text-blue-600">
                        {formatExactTime(activity.published)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatDateTime(activity.published)}
                      </div>
                    </div>

                    {/* Member Avatar/Name */}
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                        {activity.memberDisplayName.substring(0, 2).toUpperCase()}
                      </div>
                      <div className="text-xs text-center text-gray-600 mt-1 truncate w-10">
                        {activity.memberDisplayName}
                      </div>
                    </div>

                    {/* Activity Details */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          activity.activityType === 'comment' ? 'bg-green-100 text-green-800' :
                          activity.activityType === 'update' ? 'bg-blue-100 text-blue-800' :
                          activity.activityType === 'create' ? 'bg-purple-100 text-purple-800' :
                          activity.activityType === 'resolve' ? 'bg-orange-100 text-orange-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {activity.activityType}
                        </span>
                        {activity.ticketKey && (
                          <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded font-mono hover:bg-blue-700 transition-colors">
                            <TicketLink
                              ticketKey={activity.ticketKey}
                              ticketUrl={activity.ticketUrl}
                              className="text-white hover:text-blue-100"
                            />
                          </span>
                        )}
                      </div>
                      <div className="text-gray-800 text-sm leading-relaxed">
                        {activity.cleanTitle || activity.title}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-400 text-lg mb-2">📅</div>
              <div className="text-gray-600">No team activities recorded for today yet</div>
              <div className="text-gray-500 text-sm mt-1">
                Activities will appear here as your team works throughout the day
              </div>
            </div>
          )}
        </div>
      )}

      {/* Team Overview Stats */}
      {teamActivities.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Team Overview ({timeRange})</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{totalStats.tickets}</div>
              <div className="text-sm text-gray-600">Total Tickets</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{formatTime(totalStats.timeSpent)}</div>
              <div className="text-sm text-gray-600">Total Time Spent</div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{teamActivities.filter(m => !m.error).length}</div>
              <div className="text-sm text-gray-600">Active Team Members</div>
            </div>
          </div>
        </div>
      )}

      {/* AI Team Analysis */}
      {aiAnalysis && (
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">🤖 AI Team Performance Analysis</h3>
            {lastAIAnalysis && (
              <div className="text-sm text-gray-500">
                Generated: {lastAIAnalysis.toLocaleString()}
              </div>
            )}
          </div>
          <div className="prose max-w-none">
            <pre className="whitespace-pre-wrap text-sm bg-gray-50 p-4 rounded border overflow-x-auto">
              {aiAnalysis}
            </pre>
          </div>
        </div>
      )}

      {/* Prompt for AI Analysis */}
      {!aiAnalysis && teamActivities.length > 0 && (
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 text-center">
          <h3 className="text-lg font-semibold text-purple-800 mb-2">Ready for Team AI Analysis</h3>
          <p className="text-purple-700 mb-4">
            Team activity data loaded for {teamActivities.filter(m => !m.error).length} members.
            Click "🤖 AI Team Analysis" to generate delivery insights, performance analysis, and management recommendations.
          </p>
          <button
            onClick={analyzeTeamWithAI}
            className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 disabled:bg-gray-400"
            disabled={analyzingAI}
          >
            {analyzingAI ? 'Analyzing Team...' : '🤖 Generate Team Analysis'}
          </button>
        </div>
      )}

      {/* Team Member Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {teamActivities.map((memberData, index) => (
          <div key={index} className="bg-white rounded-lg shadow-lg p-6">
            {memberData.error ? (
              <div className="text-center">
                <h3 className="text-lg font-semibold text-red-600 mb-2">
                  {memberData.member}
                </h3>
                <p className="text-red-500 text-sm">Error: {memberData.error}</p>
              </div>
            ) : (
              <>
                {/* Member Header with Quick Activity Summary */}
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">
                      👤 {memberData.member}
                    </h3>
                    {memberData.stats.realActivityStats && (
                      <div className="text-sm text-blue-600 mt-1">
                        🔥 {memberData.stats.realActivityStats.totalActivities} activities •
                        {memberData.stats.realActivityStats.mostActiveDay &&
                          ` Peak: ${memberData.stats.realActivityStats.mostActiveDay.day} (${memberData.stats.realActivityStats.mostActiveDay.count})`
                        }
                      </div>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-500">
                      {formatDate(memberData.dateRange.from)} - {formatDate(memberData.dateRange.to)}
                    </div>
                    <div className="text-sm font-medium text-gray-700 mt-1">
                      {memberData.stats.totalTickets} tickets • {formatTime(memberData.stats.totalTimeSpent)}
                    </div>
                  </div>
                </div>

                {/* SECONDARY: Detailed Stats & Analysis - Collapsible */}
                <div className="mb-4">
                  <details className="bg-gray-50 rounded-lg">
                    <summary className="cursor-pointer p-4 font-medium text-gray-700 hover:bg-gray-100 rounded-lg">
                      📊 Detailed Stats & Analysis
                      <span className="text-sm text-gray-500 ml-2">
                        ({memberData.stats.totalTickets} tickets, {formatTime(memberData.stats.totalTimeSpent)})
                      </span>
                    </summary>

                    <div className="p-4 pt-0 space-y-4">
                      {/* Member Stats Grid */}
                      <div className="grid grid-cols-2 gap-3">
                        <div className="bg-white p-3 rounded border">
                          <div className="text-lg font-bold text-gray-700">{memberData.stats.totalTickets}</div>
                          <div className="text-xs text-gray-500">Total Tickets</div>
                        </div>
                        <div className="bg-white p-3 rounded border">
                          <div className="text-lg font-bold text-gray-700">{formatTime(memberData.stats.totalTimeSpent)}</div>
                          <div className="text-xs text-gray-500">Time Spent</div>
                        </div>
                        <div className="bg-white p-3 rounded border">
                          <div className="text-lg font-bold text-gray-700">{memberData.stats.avgTicketsPerDay}</div>
                          <div className="text-xs text-gray-500">Avg Tickets/Day</div>
                        </div>
                        <div className="bg-white p-3 rounded border">
                          <div className="text-lg font-bold text-gray-700">{memberData.stats.avgHoursPerDay}h</div>
                          <div className="text-xs text-gray-500">Avg Hours/Day</div>
                        </div>
                      </div>

                      {/* Status Distribution */}
                      {Object.keys(memberData.stats.ticketsByStatus).length > 0 && (
                        <div>
                          <h5 className="text-sm font-medium text-gray-700 mb-2">Ticket Status Distribution</h5>
                          <div className="flex flex-wrap gap-2">
                            {Object.entries(memberData.stats.ticketsByStatus).map(([status, count]) => (
                              <span key={status} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                                {status}: {count}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Epic Work */}
                      {memberData.stats.epicWork.length > 0 && (
                        <div>
                          <h5 className="text-sm font-medium text-gray-700 mb-2">Epic Contributions</h5>
                          <div className="space-y-1">
                            {memberData.stats.epicWork.slice(0, 3).map((epic, idx) => (
                              <div key={idx} className="text-xs bg-green-50 p-2 rounded border">
                                <div className="font-medium">{epic.epicKey}</div>
                                <div className="text-gray-600">{epic.tickets} tickets • {formatTime(epic.timeSpent)}</div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </details>
                </div>

                {/* PRIMARY: Real Activity Feed - Main Focus */}
                {memberData.realActivityFeed && memberData.realActivityFeed.length > 0 && (
                  <div className="mb-6 border-2 border-blue-200 rounded-lg bg-blue-50/30">
                    <div className="bg-blue-100 px-4 py-3 rounded-t-lg">
                      <div className="flex justify-between items-center">
                        <h4 className="text-base font-semibold text-blue-900">
                          🔥 Live Activity Feed
                        </h4>
                        <div className="flex items-center gap-3">
                          <span className="text-sm text-blue-700 font-medium">
                            {memberData.stats.realActivityStats?.totalActivities || 0} total activities
                          </span>
                          <button
                            onClick={() => toggleActivityFeed(index)}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                          >
                            {expandedActivityFeeds[index] ? '▼ Show Less' : '▶ Show More'}
                          </button>
                        </div>
                      </div>
                    </div>

                    <div className="p-4">
                      <div className={`space-y-2 overflow-y-auto ${
                        expandedActivityFeeds[index] ? 'max-h-96' : 'max-h-64'
                      }`}>
                        {memberData.realActivityFeed
                          .slice(0, expandedActivityFeeds[index] ? 25 : 12)
                          .map((activity, idx) => (
                          <div key={idx} className="bg-white p-3 rounded-lg border border-blue-200 shadow-sm hover:shadow-md transition-shadow">
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                  <span className="font-semibold text-blue-800 text-sm">
                                    {activity.activityType}
                                  </span>
                                  {activity.ticketKey && (
                                    <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded font-mono hover:bg-blue-700 transition-colors">
                                      <TicketLink
                                        ticketKey={activity.ticketKey}
                                        ticketUrl={activity.ticketUrl}
                                        className="text-white hover:text-blue-100"
                                      />
                                    </span>
                                  )}
                                </div>
                                <div className="text-gray-800 text-sm leading-relaxed">
                                  {activity.cleanTitle || activity.title}
                                </div>
                              </div>
                              <div className="text-gray-500 text-xs ml-3 text-right">
                                <div className="font-medium">{formatDateTime(activity.published)}</div>
                                <div className="text-gray-400">{formatDate(activity.published)}</div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Activity Type Summary */}
                      {memberData.stats.realActivityStats && (
                        <div className="mt-4 pt-3 border-t border-blue-200">
                          <div className="flex flex-wrap gap-2">
                            {Object.entries(memberData.stats.realActivityStats.activitiesByType || {}).map(([type, count]) => (
                              <span key={type} className="bg-blue-200 text-blue-800 text-xs px-3 py-1 rounded-full font-medium">
                                {type}: {count}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* TERTIARY: Recent Ticket Activity - Compact View */}
                {memberData.recentActivities.length > 0 && (
                  <div className="bg-gray-50 rounded-lg p-3">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">📋 Recent Ticket Updates</h4>
                    <div className="space-y-1 max-h-24 overflow-y-auto">
                      {memberData.recentActivities.slice(0, 3).map((activity, idx) => (
                        <div key={idx} className="text-xs bg-white p-2 rounded border flex justify-between items-center hover:bg-gray-50 transition-colors">
                          <div className="flex-1">
                            <span className="font-medium text-blue-600">
                              <TicketLink
                                ticketKey={activity.ticketKey}
                                className="text-blue-600 hover:text-blue-800"
                              />
                            </span>
                            <span className="text-gray-600 ml-2 truncate">{activity.ticketSummary}</span>
                          </div>
                          <div className="text-gray-500 text-xs ml-2">
                            {formatDateTime(activity.lastUpdated)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        ))}
      </div>

      {/* Technical Debug Information - Positioned at bottom for troubleshooting */}
      {fetchingSummary && (
        <div className="mt-8 border-t border-gray-200 pt-6">
          <details className="bg-gray-50 rounded-lg border border-gray-200">
            <summary className="cursor-pointer p-3 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-lg flex items-center justify-between">
              <span className="flex items-center">
                🔧 Technical Debug Information
                <span className="ml-2 text-xs text-gray-500">
                  ({fetchingSummary.successfulFetches}/{fetchingSummary.totalMembers} successful)
                </span>
              </span>
              <span className="text-xs text-gray-400">
                Click to expand for troubleshooting
              </span>
            </summary>

            <div className="p-4 pt-0 space-y-4">
              {/* Compact stats grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <div className="bg-white p-2 rounded border text-center">
                  <div className="text-lg font-semibold text-gray-700">{fetchingSummary.totalMembers}</div>
                  <div className="text-xs text-gray-500">Team Members</div>
                </div>
                <div className="bg-white p-2 rounded border text-center">
                  <div className="text-lg font-semibold text-green-600">{fetchingSummary.successfulFetches}</div>
                  <div className="text-xs text-gray-500">Successful</div>
                </div>
                <div className="bg-white p-2 rounded border text-center">
                  <div className="text-lg font-semibold text-blue-600">{fetchingSummary.totalActivitiesFetched}</div>
                  <div className="text-xs text-gray-500">Activities</div>
                </div>
                <div className="bg-white p-2 rounded border text-center">
                  <div className={`text-lg font-semibold ${fetchingSummary.failedFetches > 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {fetchingSummary.failedFetches > 0 ? fetchingSummary.failedFetches : '✅'}
                  </div>
                  <div className="text-xs text-gray-500">Failed</div>
                </div>
              </div>

              {/* Member-by-member summary - only show if there are issues */}
              {(fetchingSummary.failedFetches > 0 || fetchingSummary.memberSummaries?.some(s => s.error)) && (
                <div>
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Per-Member Status:</h5>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                    {fetchingSummary.memberSummaries?.map((summary, idx) => (
                      <div key={idx} className={`p-2 rounded text-xs border ${
                        summary.error ? 'bg-red-50 border-red-200' : 'bg-white border-gray-200'
                      }`}>
                        <div className="font-medium flex items-center">
                          {summary.error ? '❌' : '✅'} {summary.member}
                        </div>
                        <div className="text-gray-600 mt-1">
                          {summary.error ? (
                            <span className="text-red-600">{summary.error}</span>
                          ) : (
                            `${summary.activitiesFetched} activities • ${summary.ticketCount} tickets`
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Success message when everything is working */}
              {fetchingSummary.failedFetches === 0 && (
                <div className="text-center py-2">
                  <div className="text-green-600 text-sm">
                    ✅ All team member data fetched successfully
                  </div>
                  <div className="text-gray-500 text-xs mt-1">
                    No issues detected with data fetching
                  </div>
                </div>
              )}
            </div>
          </details>
        </div>
      )}
    </div>
  );
}
