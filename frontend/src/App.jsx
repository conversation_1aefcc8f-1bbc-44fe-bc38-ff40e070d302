import { useEffect, useState } from 'react';
import TicketTable from './components/TicketTable';
import Worklog<PERSON><PERSON> from './components/WorklogChart';
import TeamActivityDashboard from './components/TeamActivityDashboard';
import WeeklyActivityTimeline from './components/WeeklyActivityTimeline';
import CustomAnalysisSection from './components/CustomAnalysisSection';

export default function App() {
  // Tab management - Weekly Activity Timeline is the primary tab
  const [activeTab, setActiveTab] = useState('weekly-activity');
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(30); // minutes
  const [autoRefreshTimer, setAutoRefreshTimer] = useState(null);

  // Ticket Analysis state
  const [tickets, setTickets] = useState([]);
  const [summary, setSummary] = useState('');
  const [stats, setStats] = useState({});
  const [sources, setSources] = useState([]);
  const [loading, setLoading] = useState(false);
  const [analyzingAI, setAnalyzingAI] = useState(false);
  const [error, setError] = useState('');
  const [lastRefresh, setLastRefresh] = useState(null);
  const [lastAIAnalysis, setLastAIAnalysis] = useState(null);
  const [duration, setDuration] = useState(0);
  const [history, setHistory] = useState([]);
  const [showHistory, setShowHistory] = useState(false);
  const [showTable, setShowTable] = useState(true);

  // App configuration
  const [config, setConfig] = useState({ servers: [], defaultTimeRanges: {} });
  const [aiAvailable, setAiAvailable] = useState(false);
  const [checkingAI, setCheckingAI] = useState(true);



  // Query parameters
  const [timeRange, setTimeRange] = useState('2weeks');
  const [assignee, setAssignee] = useState('');
  const [project, setProject] = useState('');
  const [team, setTeam] = useState('');

  const fetchTickets = async (serverOverride = null) => {
    setLoading(true);
    setError('');
    setSummary(''); // Clear previous AI analysis

    const params = new URLSearchParams();
    params.append('timeRange', timeRange);
    if (assignee) params.append('assignee', assignee);
    if (project) params.append('project', project);
    if (team) params.append('team', team);
    if (serverOverride) params.append('server', serverOverride);

    try {
      const response = await fetch(`http://localhost:4000/api/tickets?${params}`);
      const data = await response.json();

      if (response.ok) {
        setTickets(data.tickets);
        setStats(data.stats);
        setSources(data.sources || []);
        setLastRefresh(new Date(data.timestamp));
        setDuration(data.duration);

        // Refresh history
        fetchHistory();
      } else {
        setError(data.error || 'Failed to load tickets');
      }
    } catch (err) {
      setError('Failed to connect to server');
    } finally {
      setLoading(false);
    }
  };

  const analyzeWithAI = async () => {
    if (tickets.length === 0) {
      setError('No tickets to analyze. Please fetch tickets first.');
      return;
    }

    setAnalyzingAI(true);
    setError('');

    try {
      const response = await fetch('http://localhost:4000/api/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tickets,
          options: { timeRange, assignee, project, team }
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setSummary(data.summary);
        setLastAIAnalysis(new Date(data.timestamp));
      } else {
        setError(data.error || 'Failed to analyze tickets');
      }
    } catch (err) {
      setError('Failed to connect to server for AI analysis');
    } finally {
      setAnalyzingAI(false);
    }
  };



  const fetchHistory = async () => {
    try {
      const response = await fetch('http://localhost:4000/api/history');
      const data = await response.json();
      setHistory(data.history || []);
    } catch (err) {
      console.error('Failed to fetch history:', err);
    }
  };

  const fetchConfig = async () => {
    try {
      const response = await fetch('http://localhost:4000/api/config');
      const data = await response.json();
      setConfig(data);


    } catch (err) {
      console.error('Failed to fetch config:', err);
    }
  };

  const checkAIAvailability = async () => {
    setCheckingAI(true);
    try {
      // Use health check to determine AI availability
      const response = await fetch('http://localhost:4000/api/health');
      const health = await response.json();

      if (health.services?.llm === 'ok') {
        setAiAvailable(true);
      } else {
        setAiAvailable(false);
        console.warn('AI service not available:', health.services?.llm);
      }
    } catch (err) {
      console.error('AI availability check failed:', err);
      setAiAvailable(false);
    } finally {
      setCheckingAI(false);
    }
  };

  // Auto-refresh functionality for team activity
  const startAutoRefresh = () => {
    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer);
    }

    const timer = setInterval(() => {
      if (activeTab === 'team-activity') {
        // Trigger refresh of team activity data
        window.dispatchEvent(new CustomEvent('refreshTeamActivity'));
      } else if (activeTab === 'weekly-activity') {
        // Trigger refresh of weekly activity data
        window.dispatchEvent(new CustomEvent('refreshWeeklyActivity'));
      }
    }, refreshInterval * 60 * 1000); // Convert minutes to milliseconds

    setAutoRefreshTimer(timer);
  };

  const stopAutoRefresh = () => {
    if (autoRefreshTimer) {
      clearInterval(autoRefreshTimer);
      setAutoRefreshTimer(null);
    }
  };

  // Handle auto-refresh toggle
  useEffect(() => {
    if (autoRefresh && (activeTab === 'team-activity' || activeTab === 'weekly-activity')) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }

    return () => stopAutoRefresh(); // Cleanup on unmount
  }, [autoRefresh, refreshInterval, activeTab]);

  useEffect(() => {
    // Load history, config, and check AI availability on component mount
    fetchHistory();
    fetchConfig();
    checkAIAvailability();
  }, []);

  const formatTime = (seconds) => {
    if (!seconds) return '0h';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  const exportAnalysis = () => {
    const exportData = {
      timestamp: new Date().toISOString(),
      parameters: { timeRange, assignee, project, team },
      statistics: stats,
      aiAnalysis: summary,
      ticketCount: tickets.length,
      totalTimeSpent: formatTime(stats.totalTimeSpent)
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `jira-analysis-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const exportSummaryText = () => {
    const text = `# Jira Delivery Analysis Report
Generated: ${new Date().toLocaleString()}
Parameters: ${timeRange} | Project: ${project || 'All'} | Assignee: ${assignee || 'All'}

## Statistics
- Total Tickets: ${stats.totalTickets}
- Total Time Spent: ${formatTime(stats.totalTimeSpent)}

## AI Analysis
${summary}

## Ticket List
${tickets.map(t => `- ${t.key}: ${t.summary} (${t.status}) - ${formatTime(t.timeSpentSeconds)}`).join('\n')}
`;

    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `jira-summary-${new Date().toISOString().split('T')[0]}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const formatDuration = (ms) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };
  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header with Tab Navigation */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-8 py-4">
          <div className="flex justify-between items-center mb-4">
            <h1 className="text-3xl font-bold text-gray-900">Delivery Lead Dashboard</h1>
            <div className="flex items-center space-x-4">
              {lastRefresh && (
                <div className="text-sm text-gray-600">
                  Last refresh: {lastRefresh.toLocaleString()} ({formatDuration(duration)})
                </div>
              )}
              {tickets.length > 0 && (
                <div className="text-sm text-gray-600">
                  {stats.totalTickets} tickets • {formatTime(stats.totalTimeSpent)} total time
                </div>
              )}
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-1">
            <button
              onClick={() => setActiveTab('weekly-activity')}
              className={`px-6 py-3 rounded-t-lg font-medium transition-colors ${
                activeTab === 'weekly-activity'
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              🗓️ Weekly Activity Timeline
              <span className="ml-2 text-xs bg-blue-500 text-white px-2 py-1 rounded-full">
                PRIMARY
              </span>
            </button>
            <button
              onClick={() => setActiveTab('team-activity')}
              className={`px-6 py-3 rounded-t-lg font-medium transition-colors ${
                activeTab === 'team-activity'
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              👥 Team Activity
            </button>
            <button
              onClick={() => setActiveTab('ticket-analysis')}
              className={`px-6 py-3 rounded-t-lg font-medium transition-colors ${
                activeTab === 'ticket-analysis'
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              📊 Ticket Analysis & Reports
            </button>
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-8 py-6 space-y-6">

        {/* Weekly Activity Timeline Tab */}
        {activeTab === 'weekly-activity' && (
          <WeeklyActivityTimeline />
        )}

        {/* Team Activity Tab */}
        {activeTab === 'team-activity' && (
          <div className="space-y-6">
            {/* Team Activity Controls */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-900">🔥 Live Team Activity Monitor</h2>
                <div className="flex items-center space-x-4">
                  {/* Auto-refresh controls */}
                  <div className="flex items-center space-x-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={autoRefresh}
                        onChange={(e) => setAutoRefresh(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">Auto-refresh</span>
                    </label>
                    {autoRefresh && (
                      <select
                        value={refreshInterval}
                        onChange={(e) => setRefreshInterval(Number(e.target.value))}
                        className="text-sm border border-gray-300 rounded px-2 py-1"
                      >
                        <option value={15}>15 min</option>
                        <option value={30}>30 min</option>
                        <option value={60}>1 hour</option>
                      </select>
                    )}
                  </div>
                  {autoRefresh && (
                    <div className="text-sm text-green-600 font-medium">
                      🔄 Auto-refreshing every {refreshInterval} minutes
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-semibold text-blue-900 mb-2">📋 Daily Team Activity Overview</h3>
                <p className="text-blue-800 text-sm">
                  Monitor what your team members are working on in real-time. This is your primary daily dashboard
                  for delivery oversight, showing live activity feeds, ticket progress, and team productivity metrics.
                </p>
                {autoRefresh && (
                  <p className="text-blue-700 text-sm mt-2">
                    ✅ <strong>Auto-refresh enabled</strong> - Keep this tab open on your monitor for continuous updates.
                  </p>
                )}
              </div>
            </div>

            {/* Team Activity Dashboard Component */}
            <TeamActivityDashboard />
          </div>
        )}

        {/* Ticket Analysis Tab */}
        {activeTab === 'ticket-analysis' && (
          <div className="space-y-6">
            {/* Analysis Control Panel */}
            <div className="bg-white rounded-2xl shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">📊 Ticket Analysis & Reports</h2>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setShowHistory(!showHistory)}
                    className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
                  >
                    {showHistory ? 'Hide' : 'Show'} History ({history.length})
                  </button>
                  {tickets.length > 0 && (
                    <>
                      <button
                        onClick={analyzeWithAI}
                        className={`px-4 py-2 rounded text-white ${
                          !aiAvailable ? 'bg-gray-400 cursor-not-allowed' :
                          analyzingAI ? 'bg-gray-400' :
                          'bg-purple-600 hover:bg-purple-700'
                        }`}
                        disabled={analyzingAI || !aiAvailable}
                        title={!aiAvailable ? 'AI service not available' : 'Generate AI analysis'}
                      >
                        {checkingAI ? '🔄 Checking AI...' :
                         !aiAvailable ? '❌ AI Unavailable' :
                         analyzingAI ? '🤖 Analyzing...' : '🤖 AI Analysis'}
                      </button>
                      <button
                        onClick={() => setShowTable(!showTable)}
                        className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700"
                      >
                        {showTable ? 'Hide' : 'Show'} Table
                      </button>
                      {summary && (
                        <button
                          onClick={exportSummaryText}
                          className="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700"
                        >
                          Export Summary
                        </button>
                      )}
                      <button
                        onClick={exportAnalysis}
                        className="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700"
                      >
                        Export Data
                      </button>
                    </>
                  )}
                </div>
              </div>

              {/* Status Display */}
              {loading && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
                    <span className="text-blue-800">Analyzing tickets with AI...</span>
                  </div>
                </div>
              )}

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <h3 className="text-red-800 font-semibold mb-2">Error Loading Tickets</h3>
                  <p className="text-red-600 mb-3">{error}</p>
                  <button
                    onClick={fetchTickets}
                    className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
                  >
                    Retry
                  </button>
                </div>
              )}

              {/* Data Sources Info */}
              {sources.length > 0 && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                  <h3 className="text-green-800 font-semibold mb-2">Data Sources</h3>
                  <ul className="text-green-700 text-sm">
                    {sources.map((source, index) => (
                      <li key={index}>• {source}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* History Panel */}
              {showHistory && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
                  <h3 className="font-semibold mb-3">Analysis History</h3>
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {history.map((entry) => (
                      <div key={entry.id} className="bg-white p-3 rounded border text-sm">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="font-medium">
                              {new Date(entry.timestamp).toLocaleString()}
                            </div>
                            <div className="text-gray-600">
                              {entry.options.timeRange} • {entry.stats.totalTickets} tickets • {formatDuration(entry.duration)}
                            </div>
                            {entry.sources && (
                              <div className="text-xs text-gray-500 mt-1">
                                Sources: {entry.sources.join(', ')}
                              </div>
                            )}
                          </div>
                          <button
                            onClick={() => {
                              setTimeRange(entry.options.timeRange);
                              setAssignee(entry.options.assignee || '');
                              setProject(entry.options.project || '');
                              setTeam(entry.options.team || '');
                            }}
                            className="text-blue-600 hover:text-blue-800 text-xs"
                          >
                            Restore
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Quick Presets */}
              <h3 className="text-lg font-medium mb-3">Delivery Lead Analysis Presets</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <button
                  onClick={() => {
                    setTimeRange('1month');
                    setAssignee('');
                    setProject('');
                    setTeam('');
                    setTimeout(() => fetchTickets('servicedesk'), 100);
                  }}
                  className="p-4 border-2 border-red-200 rounded-lg hover:border-red-400 text-left"
                  disabled={loading}
                >
                  <h4 className="font-semibold text-red-800">🚨 Service Desk Alert Analysis</h4>
                  <p className="text-sm text-gray-600">
                    <strong>Source:</strong> Knowit Service Desk ONLY (jira.knowitops.com)<br/>
                    <strong>Query:</strong> ALL tickets in OPSVIOT project, past month<br/>
                    <strong>Purpose:</strong> Find recurring alerts (Kubernetes, Airflow, etc.) that need fixing<br/>
                    <strong>For:</strong> Identifying system issues to discuss with Valmet<br/>
                    <strong>Note:</strong> Explicit service desk targeting, no user filtering
                  </p>
                </button>

                <button
                  onClick={() => {
                    setTimeRange('2weeks');
                    setAssignee(config.servers[0]?.user || '');
                    setProject(config.servers[0]?.project || '');
                    setTeam('');
                    setTimeout(() => fetchTickets('client'), 100);
                  }}
                  className="p-4 border-2 border-green-200 rounded-lg hover:border-green-400 text-left"
                  disabled={loading}
                >
                  <h4 className="font-semibold text-green-800">👥 Team Work Summary</h4>
                  <p className="text-sm text-gray-600">
                    <strong>Source:</strong> Valmet Client Jira ONLY (jira.aut.valmet.com)<br/>
                    <strong>Query:</strong> Team members ({config.servers[0]?.user || 'from JIRA_USER config'}), project {config.servers[0]?.project || 'from JIRA_PROJECTKEY config'}, past 2 weeks<br/>
                    <strong>Purpose:</strong> What our cloud architects did, who did what, how many hours<br/>
                    <strong>For:</strong> Client delivery review with Valmet<br/>
                    <strong>Note:</strong> Explicit client Jira targeting, uses JIRA_USER and JIRA_PROJECTKEY from config
                  </p>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <button
                  onClick={() => {
                    setTimeRange('1month');
                    setAssignee('');
                    setProject('');
                    setTeam('');
                    setTimeout(() => fetchTickets('both'), 100);
                  }}
                  className="p-4 border-2 border-blue-200 rounded-lg hover:border-blue-400 text-left"
                  disabled={loading}
                >
                  <h4 className="font-semibold text-blue-800">📊 Complete Monthly Review</h4>
                  <p className="text-sm text-gray-600">
                    <strong>Source:</strong> Both Jira instances<br/>
                    <strong>Query:</strong> Knowit Service Desk (OPSVIOT project only) + Valmet Client Jira (VIID project, all users), past month<br/>
                    <strong>Purpose:</strong> Full delivery review combining recurring issues and team work<br/>
                    <strong>For:</strong> Comprehensive client meeting preparation<br/>
                    <strong>Note:</strong> Explicit both servers targeting - gets everything for broad overview
                  </p>
                </button>

                <button
                  onClick={() => {
                    setTimeRange('2weeks');
                    setAssignee('');
                    setProject(config.servers[0]?.project || 'VIID');
                    setTeam('');
                    setTimeout(() => fetchTickets('client'), 100);
                  }}
                  className="p-4 border-2 border-purple-200 rounded-lg hover:border-purple-400 text-left"
                  disabled={loading}
                >
                  <h4 className="font-semibold text-purple-800">🎯 Specific Project Deep Dive</h4>
                  <p className="text-sm text-gray-600">
                    <strong>Source:</strong> Valmet Client Jira ONLY<br/>
                    <strong>Query:</strong> Project {config.servers[0]?.project || 'VIID'} only, all team members, past 2 weeks<br/>
                    <strong>Purpose:</strong> Project-specific work summary and progress<br/>
                    <strong>For:</strong> Project-focused client discussions<br/>
                    <strong>Note:</strong> Explicit client Jira targeting, uses JIRA_PROJECTKEY from config
                  </p>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <button
                  onClick={() => {
                    setActiveTab('team-activity');
                  }}
                  className="p-4 border-2 border-orange-200 rounded-lg hover:border-orange-400 text-left"
                >
                  <h4 className="font-semibold text-orange-800">👥 Team Member Activity Tracking</h4>
                  <p className="text-sm text-gray-600">
                    <strong>Source:</strong> Valmet Client Jira (individual profiles)<br/>
                    <strong>Query:</strong> Individual team member activities, tickets, and productivity<br/>
                    <strong>Purpose:</strong> Track what each team member is working on daily<br/>
                    <strong>For:</strong> Delivery lead oversight and team performance insights<br/>
                    <strong>Note:</strong> Uses team members from JIRA_USER config ({config.servers[0]?.user || 'configured in .env file'})
                  </p>
                </button>
              </div>

              {/* Enhanced Custom Analysis Section */}
              <CustomAnalysisSection
                config={config}
                timeRange={timeRange}
                setTimeRange={setTimeRange}
                assignee={assignee}
                setAssignee={setAssignee}
                project={project}
                setProject={setProject}
                team={team}
                setTeam={setTeam}
                onFetchTickets={fetchTickets}
                loading={loading}
              />
            </div>

            {/* Ticket Analysis Results Section - Only show when we have data */}
            {tickets.length > 0 && (
              <>
                {/* Data Table */}
                {showTable && (
                  <TicketTable tickets={tickets} stats={stats} config={config} />
                )}

                {/* Statistics Overview */}
                <section className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Basic Statistics */}
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white rounded-2xl shadow p-6">
                        <h3 className="text-lg font-semibold mb-3">By Status</h3>
                        <div className="space-y-2">
                          {Object.entries(stats.byStatus || {}).map(([status, count]) => (
                            <div key={status} className="flex justify-between">
                              <span className="text-sm">{status}</span>
                              <span className="font-medium">{count}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="bg-white rounded-2xl shadow p-6">
                        <h3 className="text-lg font-semibold mb-3">By Project</h3>
                        <div className="space-y-2">
                          {Object.entries(stats.byProject || {}).map(([project, count]) => (
                            <div key={project} className="flex justify-between">
                              <span className="text-sm">{project}</span>
                              <span className="font-medium">{count}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="bg-white rounded-2xl shadow p-6">
                        <h3 className="text-lg font-semibold mb-3">By Type</h3>
                        <div className="space-y-2">
                          {Object.entries(stats.byIssueType || {}).map(([type, count]) => (
                            <div key={type} className="flex justify-between">
                              <span className="text-sm">{type}</span>
                              <span className="font-medium">{count}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="bg-white rounded-2xl shadow p-6">
                        <h3 className="text-lg font-semibold mb-3">By Assignee</h3>
                        <div className="space-y-2 max-h-32 overflow-y-auto">
                          {Object.entries(stats.byAssignee || {}).map(([assignee, count]) => (
                            <div key={assignee} className="flex justify-between">
                              <span className="text-sm truncate" title={assignee}>{assignee}</span>
                              <span className="font-medium">{count}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="bg-white rounded-2xl shadow p-6">
                        <h3 className="text-lg font-semibold mb-3">By Epic</h3>
                        <div className="space-y-2 max-h-32 overflow-y-auto">
                          {Object.entries(stats.byEpic || {}).map(([epic, count]) => (
                            <div key={epic} className="flex justify-between">
                              <span className="text-sm truncate" title={epic}>{epic}</span>
                              <span className="font-medium">{count}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Charts and Visualizations */}
                  <div>
                    <WorklogChart stats={stats} />
                  </div>
                </section>

                {/* AI Analysis */}
                {summary && (
                  <section className="bg-white rounded-2xl shadow p-6">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-semibold">🤖 AI Delivery Analysis</h2>
                      {lastAIAnalysis && (
                        <span className="text-sm text-gray-600">
                          Generated: {lastAIAnalysis.toLocaleString()}
                        </span>
                      )}
                    </div>
                    <div className="prose max-w-none">
                      <pre className="whitespace-pre-wrap font-sans text-sm leading-relaxed bg-gray-50 p-4 rounded-lg border">
                        {summary}
                      </pre>
                    </div>
                  </section>
                )}

                {/* Prompt for AI Analysis */}
                {!summary && tickets.length > 0 && (
                  <section className="bg-blue-50 border border-blue-200 rounded-2xl p-6 text-center">
                    <h3 className="text-lg font-semibold text-blue-800 mb-2">Ready for AI Analysis</h3>
                    <p className="text-blue-700 mb-4">
                      {tickets.length} tickets loaded. Click "🤖 AI Analysis" to generate delivery insights,
                      pattern analysis, and recommendations.
                    </p>
                    <button
                      onClick={analyzeWithAI}
                      className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-400"
                      disabled={analyzingAI}
                    >
                      {analyzingAI ? 'Analyzing...' : '🤖 Generate AI Analysis'}
                    </button>
                  </section>
                )}

                {/* Detailed Tickets */}
                <section>
                  <h2 className="text-xl font-semibold mb-4">Ticket Details ({tickets.length})</h2>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {tickets.map((t) => (
                      <div key={t.key} className="bg-white rounded-2xl shadow p-4 flex flex-col">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-semibold text-sm">
                            {t.key}: {t.summary}
                          </h3>
                          <div className="flex flex-col items-end space-y-1">
                            <span className="text-xs px-2 py-0.5 rounded bg-blue-100 text-blue-800">
                              {t.status}
                            </span>
                            <span className="text-xs px-2 py-0.5 rounded bg-gray-100 text-gray-800">
                              {t.issueType}
                            </span>
                          </div>
                        </div>

                        <div className="text-xs text-gray-600 mb-2">
                          <div>{t.project} • {t.assignee || 'Unassigned'}</div>
                          {t.timeSpentSeconds > 0 && (
                            <div className="font-medium text-green-600">
                              Time: {formatTime(t.timeSpentSeconds)}
                            </div>
                          )}
                        </div>

                        <p className="text-sm flex-grow line-clamp-3 mb-2 whitespace-pre-wrap">
                          {t.description || '—'}
                        </p>

                        {t.components.length > 0 && (
                          <div className="text-xs mb-2">
                            <span className="text-gray-500">Components: </span>
                            {t.components.join(', ')}
                          </div>
                        )}

                        <div className="text-xs text-gray-500 mt-auto">
                          Updated {new Date(t.updated).toLocaleDateString()}
                        </div>
                      </div>
                    ))}
                  </div>
                </section>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
