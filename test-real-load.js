#!/usr/bin/env node

/**
 * Test REAL LOAD - Full month service desk analysis (64+ tickets)
 */

import axios from 'axios';

const API_BASE = 'http://localhost:4000';

async function testRealLoad() {
  try {
    console.log('🔍 Testing REAL LOAD - Full Month Service Desk Analysis');
    console.log('=' + '='.repeat(60));
    
    console.log('\n1️⃣ Fetching FULL month of Service Desk tickets...');
    const ticketsResponse = await axios.get(`${API_BASE}/api/tickets`, {
      params: {
        timeRange: '1month',
        server: 'servicedesk',
        maxResults: 200  // Get all tickets for the month
      }
    });
    
    const tickets = ticketsResponse.data.tickets;
    console.log(`   ✅ Fetched ${tickets.length} tickets (real monthly load)`);
    
    if (tickets.length < 50) {
      console.log('   ⚠️  Lower ticket count than expected - may not trigger chunking');
    } else if (tickets.length >= 64) {
      console.log('   🎯 Perfect! This matches your real monthly load (64+ tickets)');
    }
    
    console.log('\n2️⃣ Running FULL AI Analysis (this will take 2-4 minutes)...');
    console.log('   📊 Processing ALL tickets with chunking if needed');
    console.log('   🎯 Testing real delivery lead workflow');
    
    const startTime = Date.now();
    
    const analysisResponse = await axios.post(`${API_BASE}/api/analyze`, {
      tickets: tickets,
      options: {
        scenario: 'Monthly Service Desk Analysis - Full Load Test'
      }
    }, {
      timeout: 300000 // 5 minutes for full analysis
    });
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    const analysis = analysisResponse.data.summary;
    const processedTickets = analysisResponse.data.ticketCount;
    
    console.log(`\n✅ FULL ANALYSIS COMPLETED!`);
    console.log(`   📊 Tickets processed: ${processedTickets}/${tickets.length}`);
    console.log(`   ⏱️  Total time: ${duration} seconds (${Math.round(duration/60)} minutes)`);
    console.log(`   📝 Analysis length: ${analysis.length} characters`);
    
    // Verify all tickets were processed
    if (processedTickets === tickets.length) {
      console.log('\n🎉 SUCCESS: ALL TICKETS PROCESSED!');
      console.log('   ✅ No data loss - complete monthly analysis');
    } else {
      console.log('\n❌ ISSUE: Missing tickets in analysis');
      console.log(`   Expected: ${tickets.length}, Got: ${processedTickets}`);
    }
    
    // Quality assessment
    console.log('\n📊 ANALYSIS QUALITY ASSESSMENT:');
    const qualityChecks = {
      'Has specific frequencies': /\d+\s+(tickets?|alerts?|issues?)/i.test(analysis),
      'Contains AWS patterns': /aws/i.test(analysis),
      'Contains Kubernetes patterns': /kubernetes|k8s/i.test(analysis),
      'Has recommendations': /recommend|suggest/i.test(analysis),
      'Business impact focus': /business|impact|client/i.test(analysis),
      'Critical items identified': /critical|urgent|priority/i.test(analysis),
      'Resolution analysis': /resolution|quick|complex/i.test(analysis),
      'Proactive suggestions': /proactive|prevent|future/i.test(analysis)
    };
    
    let passedChecks = 0;
    Object.entries(qualityChecks).forEach(([check, passed]) => {
      console.log(`   ${passed ? '✅' : '❌'} ${check}`);
      if (passed) passedChecks++;
    });
    
    const qualityScore = Math.round((passedChecks / Object.keys(qualityChecks).length) * 100);
    console.log(`\n🎯 Quality Score: ${qualityScore}% (${passedChecks}/${Object.keys(qualityChecks).length} checks passed)`);
    
    if (qualityScore >= 80) {
      console.log('🌟 EXCELLENT: High-quality analysis suitable for client meetings');
    } else if (qualityScore >= 60) {
      console.log('👍 GOOD: Analysis meets delivery lead requirements');
    } else {
      console.log('⚠️  NEEDS IMPROVEMENT: Analysis may need prompt tuning');
    }
    
    // Show analysis sample
    console.log('\n📄 ANALYSIS SAMPLE (first 800 characters):');
    console.log('─'.repeat(80));
    console.log(analysis.substring(0, 800) + '...');
    console.log('─'.repeat(80));
    
    return {
      totalTickets: tickets.length,
      processedTickets: processedTickets,
      duration: duration,
      qualityScore: qualityScore,
      analysisLength: analysis.length,
      success: processedTickets === tickets.length && qualityScore >= 60
    };
    
  } catch (error) {
    console.error('\n❌ Real load test failed:', error.message);
    
    if (error.code === 'ECONNABORTED') {
      console.log('   ⏰ Timeout - large dataset processing took too long');
      console.log('   💡 This is expected for very large datasets with LM Studio');
    }
    
    return { success: false, error: error.message };
  }
}

async function main() {
  console.log('🚀 REAL LOAD TEST - Monthly Service Desk Analysis\n');
  
  // Check server
  try {
    await axios.get(`${API_BASE}/api/health`);
    console.log('✅ Server is running\n');
  } catch (error) {
    console.log('❌ Server not running. Start with: npm run dev');
    process.exit(1);
  }
  
  const result = await testRealLoad();
  
  if (result.success) {
    console.log('\n🎯 REAL LOAD TEST: PASSED ✅');
    console.log('   Your monthly service desk analysis can handle the full load!');
    console.log(`   📊 ${result.totalTickets} tickets → High-quality analysis in ${Math.round(result.duration/60)} minutes`);
  } else {
    console.log('\n⚠️  REAL LOAD TEST: NEEDS ATTENTION');
    console.log('   Some optimization may be needed for full monthly loads');
  }
}

main().catch(console.error);
