#!/usr/bin/env node

/**
 * Test the enhanced AI analysis with improved prompts and data
 */

import axios from 'axios';

const API_BASE = 'http://localhost:4000';

async function testEnhancedAI() {
  try {
    console.log('🔍 Testing Enhanced AI Analysis...\n');
    
    console.log('1️⃣ Fetching Service Desk tickets...');
    const ticketsResponse = await axios.get(`${API_BASE}/api/tickets`, {
      params: {
        timeRange: '1month',
        server: 'servicedesk',
        maxResults: 30 // Smaller set for faster testing
      }
    });
    
    const tickets = ticketsResponse.data.tickets;
    console.log(`   ✅ Fetched ${tickets.length} tickets`);
    
    console.log('\n2️⃣ Running Enhanced AI Analysis...');
    console.log('   🎯 Testing improved prompts and data structure');
    
    const startTime = Date.now();
    
    const analysisResponse = await axios.post(`${API_BASE}/api/analyze`, {
      tickets: tickets,
      options: {
        scenario: 'Enhanced Service Desk Alert Analysis'
      }
    }, {
      timeout: 180000 // 3 minutes
    });
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    const analysis = analysisResponse.data.summary;
    
    console.log(`   ✅ Analysis completed in ${duration} seconds`);
    console.log(`   📝 Analysis length: ${analysis.length} characters`);
    
    console.log('\n📄 ENHANCED AI ANALYSIS OUTPUT:');
    console.log('=' + '='.repeat(80));
    console.log(analysis);
    console.log('=' + '='.repeat(80));
    
    console.log('\n🔍 ENHANCEMENT QUALITY CHECK:');
    const checks = {
      'Contains specific frequencies': /\d+\s+(tickets?|alerts?|issues?)/i.test(analysis),
      'Has prioritized recommendations': /high priority|medium priority|priority/i.test(analysis),
      'Includes resolution times': /resolution|quick|complex|hours?|days?/i.test(analysis),
      'Has business impact focus': /business|impact|client|service/i.test(analysis),
      'Contains risk assessment': /risk|trend|pattern|future/i.test(analysis),
      'Actionable for delivery lead': /recommend|suggest|action|discuss/i.test(analysis)
    };
    
    Object.entries(checks).forEach(([check, passed]) => {
      console.log(`   ${passed ? '✅' : '❌'} ${check}`);
    });
    
    const passedChecks = Object.values(checks).filter(Boolean).length;
    const totalChecks = Object.keys(checks).length;
    
    console.log(`\n📊 Enhancement Score: ${passedChecks}/${totalChecks} (${Math.round(passedChecks/totalChecks*100)}%)`);
    
    if (passedChecks >= totalChecks * 0.8) {
      console.log('🎉 EXCELLENT: Enhanced AI analysis is working well!');
    } else if (passedChecks >= totalChecks * 0.6) {
      console.log('👍 GOOD: Enhanced AI analysis shows improvement');
    } else {
      console.log('⚠️  NEEDS WORK: Enhancement may need further tuning');
    }
    
    return {
      analysis,
      duration,
      enhancementScore: passedChecks / totalChecks,
      ticketCount: tickets.length
    };
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.code === 'ECONNABORTED') {
      console.log('   ⏰ Timeout - analysis taking longer than expected');
    } else if (error.response?.status === 500) {
      console.log('   🔧 Server error - check backend logs');
    }
    
    return { success: false, error: error.message };
  }
}

async function main() {
  console.log('🚀 Testing Enhanced AI Analysis\n');
  
  // Check server health
  try {
    await axios.get(`${API_BASE}/api/health`);
    console.log('✅ Server is running\n');
  } catch (error) {
    console.log('❌ Server not running. Start with: npm run dev');
    process.exit(1);
  }
  
  const result = await testEnhancedAI();
  
  if (result.success !== false) {
    console.log('\n🎯 SUMMARY:');
    console.log(`   📊 Processed ${result.ticketCount} tickets`);
    console.log(`   ⏱️  Analysis time: ${result.duration} seconds`);
    console.log(`   🎯 Enhancement score: ${Math.round(result.enhancementScore * 100)}%`);
    console.log('\n💡 The enhanced AI analysis provides more detailed, actionable insights');
    console.log('   for your monthly service desk reviews and client discussions.');
  }
}

main().catch(console.error);
