#!/usr/bin/env node

/**
 * Test exact pattern counting - focus on getting precise Airflow frequencies
 */

import axios from 'axios';

const API_BASE = 'http://localhost:4000';

async function testExactCounting() {
  try {
    console.log('🔢 Testing Exact Pattern Counting');
    console.log('🎯 Goal: Get precise frequency count for Airflow heartbeat alarms');
    console.log('=' + '='.repeat(80));
    
    console.log('\n1️⃣ Fetching and analyzing raw data...');
    const ticketsResponse = await axios.get(`${API_BASE}/api/tickets`, {
      params: {
        timeRange: '1month',
        server: 'servicedesk',
        maxResults: 100
      }
    });
    
    const tickets = ticketsResponse.data.tickets;
    console.log(`   ✅ Fetched ${tickets.length} tickets`);
    
    // Manual pattern analysis
    const patterns = {};
    tickets.forEach(ticket => {
      const summary = ticket.summary?.toLowerCase() || '';
      
      if (summary.includes('airflow') && summary.includes('heartbeat')) {
        patterns['Airflow Heartbeat'] = (patterns['Airflow Heartbeat'] || 0) + 1;
      }
      if (summary.includes('certificate') && summary.includes('expir')) {
        patterns['Certificate Expiration'] = (patterns['Certificate Expiration'] || 0) + 1;
      }
      if (summary.includes('kubernetes') || summary.includes('node')) {
        patterns['Kubernetes/Node Issues'] = (patterns['Kubernetes/Node Issues'] || 0) + 1;
      }
    });
    
    console.log('\n📊 MANUAL PATTERN ANALYSIS (Ground Truth):');
    Object.entries(patterns).forEach(([pattern, count]) => {
      console.log(`   • ${pattern}: ${count} tickets`);
    });
    
    const expectedAirflowCount = patterns['Airflow Heartbeat'] || 0;
    console.log(`\n🎯 Expected Airflow count: ${expectedAirflowCount}`);
    
    console.log('\n2️⃣ Running AI Analysis...');
    console.log('   🔢 Testing if AI can match the exact counts');
    
    const startTime = Date.now();
    
    const analysisResponse = await axios.post(`${API_BASE}/api/analyze`, {
      tickets: tickets,
      options: {
        scenario: 'Exact Counting Test - Service Desk Analysis'
      }
    }, {
      timeout: 300000
    });
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    const analysis = analysisResponse.data.summary;
    
    console.log(`\n✅ Analysis completed in ${duration} seconds`);
    
    // Extract frequency mentions from AI analysis
    console.log('\n🔍 AI FREQUENCY ANALYSIS:');
    
    const airflowMatches = analysis.match(/airflow[^:]*:\s*(\d+)/gi) || 
                          analysis.match(/(\d+)[^.]*airflow/gi) ||
                          analysis.match(/heartbeat[^:]*:\s*(\d+)/gi);
    
    let aiAirflowCount = 0;
    if (airflowMatches) {
      console.log('   📋 Airflow frequency mentions found:');
      airflowMatches.forEach(match => {
        console.log(`      • "${match}"`);
        const numbers = match.match(/\d+/g);
        if (numbers) {
          const count = parseInt(numbers[0]);
          if (count > aiAirflowCount) aiAirflowCount = count;
        }
      });
    } else {
      console.log('   ❌ No Airflow frequency mentions found');
    }
    
    console.log(`\n📊 COMPARISON:');
    console.log(`   🎯 Expected Airflow count: ${expectedAirflowCount}`);
    console.log(`   🤖 AI detected count: ${aiAirflowCount}`);
    console.log(`   📈 Accuracy: ${aiAirflowCount === expectedAirflowCount ? '✅ PERFECT' : `❌ Off by ${Math.abs(expectedAirflowCount - aiAirflowCount)}`}`);
    
    // Show relevant analysis sections
    console.log('\n📄 AIRFLOW ANALYSIS SECTIONS:');
    console.log('─'.repeat(80));
    
    const analysisLines = analysis.split('\n');
    const airflowLines = analysisLines.filter(line => 
      /airflow|heartbeat/i.test(line) && line.trim().length > 0
    );
    
    airflowLines.forEach(line => console.log(line.trim()));
    
    console.log('─'.repeat(80));
    
    // Overall assessment
    const accuracy = expectedAirflowCount > 0 ? (aiAirflowCount / expectedAirflowCount) : 0;
    
    if (aiAirflowCount === expectedAirflowCount) {
      console.log('\n🎉 PERFECT: AI counting is 100% accurate!');
      console.log('   ✅ Ready for precise delivery lead reporting');
    } else if (accuracy >= 0.8) {
      console.log('\n👍 GOOD: AI counting is close to accurate');
      console.log('   ⚠️  Minor discrepancy but usable for delivery lead');
    } else {
      console.log('\n❌ NEEDS IMPROVEMENT: AI counting is significantly off');
      console.log('   🔧 Pattern recognition needs further optimization');
    }
    
    return {
      expectedCount: expectedAirflowCount,
      aiCount: aiAirflowCount,
      accuracy: accuracy,
      perfect: aiAirflowCount === expectedAirflowCount
    };
    
  } catch (error) {
    console.error('\n❌ Exact counting test failed:', error.message);
    return { success: false, error: error.message };
  }
}

async function main() {
  console.log('🧪 EXACT PATTERN COUNTING TEST\n');
  
  try {
    await axios.get(`${API_BASE}/api/health`);
    console.log('✅ Server is running\n');
  } catch (error) {
    console.log('❌ Server not running. Please start backend server.');
    process.exit(1);
  }
  
  const result = await testExactCounting();
  
  if (result.success !== false) {
    console.log('\n🎯 EXACT COUNTING SUMMARY:');
    console.log(`   📊 Expected: ${result.expectedCount} Airflow tickets`);
    console.log(`   🤖 AI detected: ${result.aiCount} Airflow tickets`);
    console.log(`   📈 Accuracy: ${Math.round(result.accuracy * 100)}%`);
    
    if (result.perfect) {
      console.log('\n🎉 SUCCESS: Perfect pattern counting achieved!');
      console.log('   Your delivery lead analysis will have exact frequencies.');
    } else {
      console.log('\n⚠️  Pattern counting needs refinement for perfect accuracy');
    }
  }
}

main().catch(console.error);
