import { summarizeTickets, analyzeTeamPerformance, analyzeWeeklyActivityTimeline } from '../services/ai/analysis.js';

/**
 * Setup AI analysis routes
 */
export function setupAnalysisRoutes(app, config) {
  // AI Analysis endpoint - separate from data fetching
  app.post('/api/analyze', async (req, res, next) => {
    try {
      const { tickets, options = {} } = req.body;

      if (!tickets || !Array.isArray(tickets)) {
        return res.status(400).json({ error: 'Tickets array is required' });
      }

      console.log(`🤖 Starting AI analysis for ${tickets.length} tickets`);
      const startTime = new Date();

      const summary = await summarizeTickets(tickets, options, config);

      const endTime = new Date();
      const duration = endTime - startTime;

      res.json({
        summary,
        timestamp: endTime.toISOString(),
        duration,
        ticketCount: tickets.length
      });
    } catch (err) {
      next(err);
    }
  });

  // Team Activity AI Analysis endpoint
  app.post('/api/analyze-team', async (req, res, next) => {
    try {
      const { teamActivities, options = {} } = req.body;

      if (!teamActivities || !Array.isArray(teamActivities)) {
        return res.status(400).json({ error: 'Team activities array is required' });
      }

      console.log(`🤖 Starting AI team analysis for ${teamActivities.length} team members`);
      const startTime = new Date();

      const summary = await analyzeTeamPerformance(teamActivities, options, config);

      const endTime = new Date();
      const duration = endTime - startTime;

      res.json({
        summary,
        timestamp: endTime.toISOString(),
        duration,
        teamMemberCount: teamActivities.length
      });
    } catch (err) {
      next(err);
    }
  });

  // Weekly Activity Timeline AI Analysis endpoint
  app.post('/api/analyze-weekly-activity', async (req, res, next) => {
    try {
      const { combinedActivities, options = {} } = req.body;

      if (!combinedActivities || !Array.isArray(combinedActivities)) {
        return res.status(400).json({ error: 'Combined activities array is required' });
      }

      console.log(`🤖 Starting AI weekly activity timeline analysis for ${combinedActivities.length} activities`);
      const startTime = new Date();

      const summary = await analyzeWeeklyActivityTimeline(combinedActivities, options, config);

      const endTime = new Date();
      const duration = endTime - startTime;

      res.json({
        summary,
        timestamp: endTime.toISOString(),
        duration,
        activityCount: combinedActivities.length
      });
    } catch (err) {
      next(err);
    }
  });
}
