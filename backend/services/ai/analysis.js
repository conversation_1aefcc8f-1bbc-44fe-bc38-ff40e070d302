import axios from 'axios';

/**
 * Summarize tickets using AI
 */
export async function summarizeTickets(tickets, options = {}, config) {
  let userPrompt;
  let estimatedTokens;

  if (options.summaryMode && options.textToSummarize) {
    // Summary of summaries mode
    estimatedTokens = Math.ceil(options.textToSummarize.length / 4);
    const chunkInfo = options.totalChunks ? ` from ${options.totalChunks} chunks (${options.totalTicketsProcessed} total tickets)` : '';
    console.log(`📝 Creating summary of summaries${chunkInfo} (~${estimatedTokens} tokens)`);

    userPrompt = `Create a comprehensive delivery summary from these analysis chunks${chunkInfo}:

${options.textToSummarize}

Combine the insights into a unified analysis for delivery lead review and client meeting preparation. Ensure all ticket patterns and insights from every chunk are included in the final summary.`;
  } else {
    // Regular ticket analysis mode
    const ticketDataSize = JSON.stringify(tickets).length;
    estimatedTokens = Math.ceil(ticketDataSize / 4);

    const chunkInfo = options.chunk ? ` (Chunk ${(options.chunkIndex || 0) + 1})` : '';
    console.log(`📝 Processing ${tickets.length} tickets${chunkInfo} (~${estimatedTokens} tokens)`);

    // Prepare enhanced ticket data for AI analysis
    const ticketSummaries = tickets.map(ticket => {
      // Calculate resolution time for closed tickets
      let resolutionTime = null;
      if (ticket.status?.toLowerCase().includes('closed') || ticket.status?.toLowerCase().includes('resolved')) {
        const created = new Date(ticket.created);
        const updated = new Date(ticket.updated);
        const diffHours = Math.round((updated - created) / (1000 * 60 * 60));
        resolutionTime = diffHours < 24 ? `${diffHours}h` : `${Math.round(diffHours / 24)}d`;
      }

      return {
        key: ticket.key,
        summary: ticket.summary,
        status: ticket.status,
        assignee: ticket.assignee || 'Unassigned',
        epic: ticket.epic ? `${ticket.epic.key}: ${ticket.epic.summary}` : 'No Epic',
        timeSpent: ticket.timeSpentSeconds ? `${Math.round(ticket.timeSpentSeconds / 3600)}h` : '0h',
        source: ticket.sourceServer,
        created: ticket.created?.split('T')[0],
        updated: ticket.updated?.split('T')[0],
        resolutionTime: resolutionTime,
        priority: ticket.priority || 'Unknown',
        // Include first 100 chars of description for context if available
        description: ticket.description ? ticket.description.substring(0, 100) + '...' : 'No description'
      };
    });

    userPrompt = `Analyze these ${tickets.length} Jira tickets${chunkInfo}:

${JSON.stringify(ticketSummaries, null, 2)}

Provide insights for delivery lead review and client meeting preparation.`;
  }

  // If we're approaching token limit, summarize in chunks
  if (estimatedTokens > 50000 || tickets.length > 75) {
    console.log(`🔄 Using chunking strategy for large dataset`);
    return await summarizeInChunks(tickets, options, config);
  }

  // Determine the appropriate system prompt based on options
  let systemPrompt = getSystemPrompt(tickets, options);

  const messages = [
    {
      role: 'system',
      content: systemPrompt,
    },
    {
      role: 'user',
      content: userPrompt,
    },
  ];

  try {
    console.log(`🤖 Sending request to LM Studio (timeout: ${config.llm.timeout}ms)...`);

    const { data } = await axios.post(
      config.llm.endpoint,
      {
        model: config.llm.model,
        messages,
        temperature: 0.2,
        max_tokens: 2000,
      },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: config.llm.timeout,
      }
    );

    const analysis = data.choices?.[0]?.message?.content || '';
    console.log(`✅ LM Studio analysis completed (${analysis.length} characters)`);
    return analysis;

  } catch (error) {
    console.error(`❌ LM Studio error: ${error.message}`);

    if (error.code === 'ECONNABORTED') {
      throw new Error(`LM Studio timeout after ${config.llm.timeout}ms. The model may be processing slowly.`);
    } else if (error.code === 'ECONNREFUSED') {
      throw new Error('LM Studio is not running. Please start LM Studio and load a model.');
    } else if (error.response?.status === 404) {
      throw new Error(`Model "${config.llm.model}" not found in LM Studio. Please load the correct model.`);
    } else {
      throw new Error(`LM Studio analysis failed: ${error.message}`);
    }
  }
}

/**
 * Analyze weekly activity timeline using AI
 */
export async function analyzeWeeklyActivityTimeline(combinedActivities, options = {}, config) {
  if (!combinedActivities || combinedActivities.length === 0) {
    throw new Error('No weekly activities provided for analysis');
  }

  // Group activities by day for better analysis
  const activitiesByDay = {};
  const activityTypes = {};
  const memberContributions = {};

  combinedActivities.forEach(activity => {
    const date = new Date(activity.published).toLocaleDateString();
    const member = activity.memberName;
    const type = activity.activityType;

    // Group by day
    if (!activitiesByDay[date]) {
      activitiesByDay[date] = [];
    }
    activitiesByDay[date].push(activity);

    // Count activity types
    activityTypes[type] = (activityTypes[type] || 0) + 1;

    // Count member contributions
    memberContributions[member] = (memberContributions[member] || 0) + 1;
  });

  const systemPrompt = `ROLE: You are a delivery lead assistant analyzing weekly team activity timeline for Knowit-Valmet cloud architects team.

CONTEXT:
- Team: Cloud architects working on Valmet Industrial Internet DevOps
- Purpose: Weekly delivery overview and timeline analysis for delivery lead
- Audience: Delivery lead preparing for weekly reviews and team management
- Data: Chronological activity feed from team members throughout the week

ANALYSIS PRIORITIES:
1. WEEKLY FLOW & MOMENTUM:
   - Daily activity patterns and team momentum throughout the week
   - Peak productivity periods and quiet periods
   - Weekly progression and accomplishment flow

2. ACTIVITY PATTERNS & FOCUS AREAS:
   - Types of work being done (development, reviews, testing, etc.)
   - Ticket progression and completion patterns
   - Areas of team focus and concentration

3. COLLABORATION & COORDINATION:
   - Cross-team collaboration visible in activities
   - Coordination patterns and handoffs
   - Knowledge sharing and review activities

4. DELIVERY INSIGHTS:
   - Weekly accomplishments and deliverables
   - Progress indicators and completion trends
   - Potential blockers or areas needing attention

5. TEAM DYNAMICS:
   - Individual contribution patterns within the weekly flow
   - Workload distribution across the week
   - Team synchronization and coordination

OUTPUT FORMAT:
- Start with a brief weekly summary (2-3 sentences)
- Provide day-by-day highlights if significant patterns emerge
- Include activity type analysis and focus areas
- Highlight key accomplishments and progress indicators
- Note any coordination or collaboration patterns
- End with delivery lead recommendations for the upcoming week

Keep the analysis focused on weekly timeline insights rather than individual performance evaluation.`;

  // Format activities for AI analysis
  const weekStart = new Date(Math.min(...combinedActivities.map(a => new Date(a.published))));
  const weekEnd = new Date(Math.max(...combinedActivities.map(a => new Date(a.published))));

  const userPrompt = `WEEKLY ACTIVITY TIMELINE ANALYSIS

WEEK OVERVIEW:
- Period: ${weekStart.toLocaleDateString()} to ${weekEnd.toLocaleDateString()}
- Total Activities: ${combinedActivities.length}
- Team Members: ${Object.keys(memberContributions).join(', ')}
- Activity Types: ${Object.entries(activityTypes).map(([type, count]) => `${type} (${count})`).join(', ')}

DAILY BREAKDOWN:
${Object.entries(activitiesByDay)
  .sort(([a], [b]) => new Date(a) - new Date(b))
  .map(([date, activities]) => `
${date} (${activities.length} activities):
${activities.slice(0, 10).map(activity =>
  `  • ${activity.memberName}: ${activity.activityType} - ${activity.cleanTitle || activity.title}${activity.ticketKey ? ` [${activity.ticketKey}]` : ''}`
).join('\n')}${activities.length > 10 ? `\n  ... and ${activities.length - 10} more activities` : ''}`)
  .join('\n')}

MEMBER CONTRIBUTIONS:
${Object.entries(memberContributions)
  .sort(([,a], [,b]) => b - a)
  .map(([member, count]) => `- ${member}: ${count} activities`)
  .join('\n')}

Please analyze this weekly activity timeline focusing on delivery patterns, team coordination, and weekly accomplishments.`;

  const messages = [
    {
      role: 'system',
      content: systemPrompt,
    },
    {
      role: 'user',
      content: userPrompt,
    },
  ];

  try {
    console.log(`🤖 Sending weekly activity timeline analysis request to LM Studio...`);

    const { data } = await axios.post(
      config.llm.endpoint,
      {
        model: config.llm.model,
        messages,
        temperature: 0.2,
        max_tokens: 2500,
      },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: config.llm.timeout,
      }
    );

    const analysis = data.choices?.[0]?.message?.content || '';
    console.log(`✅ Weekly activity timeline analysis completed (${analysis.length} characters)`);
    return analysis;

  } catch (error) {
    console.error(`❌ LM Studio error: ${error.message}`);

    if (error.code === 'ECONNABORTED') {
      throw new Error(`LM Studio timeout after ${config.llm.timeout}ms. The model may be processing slowly.`);
    } else if (error.code === 'ECONNREFUSED') {
      throw new Error('LM Studio is not running. Please start LM Studio and load a model.');
    } else if (error.response?.status === 404) {
      throw new Error(`Model "${config.llm.model}" not found in LM Studio. Please load the correct model.`);
    } else {
      throw new Error(`LM Studio analysis failed: ${error.message}`);
    }
  }
}

/**
 * Analyze team performance using AI
 */
export async function analyzeTeamPerformance(teamActivities, options = {}, config) {
  const { timeRange = '2weeks' } = options;

  // Prepare team data for AI analysis
  const teamSummary = teamActivities.map(member => {
    if (member.error) {
      return {
        member: member.member,
        status: 'error',
        error: member.error
      };
    }

    return {
      member: member.member,
      totalTickets: member.stats.totalTickets,
      totalTimeSpent: `${Math.round(member.stats.totalTimeSpent / 3600)}h`,
      avgTicketsPerDay: member.stats.avgTicketsPerDay,
      avgHoursPerDay: member.stats.avgHoursPerDay,
      ticketsByStatus: member.stats.ticketsByStatus,
      ticketsByType: member.stats.ticketsByType,
      epicContributions: member.stats.epicWork?.length || 0,
      topEpics: (member.stats.epicWork || []).slice(0, 3).map(epic => ({
        epic: epic.epicKey,
        tickets: epic.tickets,
        timeSpent: `${Math.round(epic.timeSpent / 3600)}h`
      })),
      recentActivityCount: member.recentActivities?.length || 0,
      realActivityStats: member.stats.realActivityStats || {},
      dateRange: member.dateRange
    };
  });

  const systemPrompt = `ROLE: You are a delivery lead assistant analyzing team performance for Knowit-Valmet cloud architects team.

CONTEXT:
- Team: Cloud architects working on Valmet Industrial Internet DevOps
- Purpose: Delivery lead needs insights on team productivity and workload distribution
- Audience: Delivery lead preparing for team management and client discussions

ANALYSIS PRIORITIES:
1. TEAM PRODUCTIVITY ASSESSMENT:
   - Individual performance patterns and workload distribution
   - Identify high performers and those who might need support
   - Workload balance across team members

2. COLLABORATION & SPECIALIZATION:
   - Epic contributions and specialization areas
   - Knowledge sharing opportunities based on activity patterns

3. DELIVERY INSIGHTS:
   - Team velocity and capacity utilization
   - Bottlenecks or resource constraints
   - Quality of work (resolution rates, time efficiency)

4. MANAGEMENT RECOMMENDATIONS:
   - Workload rebalancing suggestions
   - Skill development opportunities
   - Team optimization strategies

OUTPUT FORMAT:
## 👥 Team Performance Analysis (${timeRange})

### 📊 Team Overview
[High-level team performance summary]

### 🎯 Individual Performance Insights
**[Member Name]**: [Performance summary, strengths, areas for attention]

### ⚖️ Workload Distribution Analysis
[Analysis of work distribution, balance, and utilization]

### 💡 Delivery Lead Recommendations
- **Immediate Actions**: [Urgent items requiring attention]
- **Workload Optimization**: [Suggestions for better distribution]
- **Skill Development**: [Training or mentoring opportunities]

Keep analysis focused on actionable insights for delivery management.`;

  const userPrompt = `Analyze this team performance data for delivery lead insights:

${JSON.stringify(teamSummary, null, 2)}

Provide comprehensive team analysis focusing on productivity, collaboration, and management recommendations.`;

  const messages = [
    {
      role: 'system',
      content: systemPrompt,
    },
    {
      role: 'user',
      content: userPrompt,
    },
  ];

  try {
    console.log(`🤖 Sending team analysis request to LM Studio...`);

    const { data } = await axios.post(
      config.llm.endpoint,
      {
        model: config.llm.model,
        messages,
        temperature: 0.2,
        max_tokens: 2500,
      },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: config.llm.timeout,
      }
    );

    const analysis = data.choices?.[0]?.message?.content || '';
    console.log(`✅ Team analysis completed (${analysis.length} characters)`);
    return analysis;

  } catch (error) {
    console.error(`❌ Team analysis error: ${error.message}`);

    if (error.code === 'ECONNABORTED') {
      throw new Error(`LM Studio timeout after ${config.llm.timeout}ms. The model may be processing slowly.`);
    } else if (error.code === 'ECONNREFUSED') {
      throw new Error('LM Studio is not running. Please start LM Studio and load a model.');
    } else if (error.response?.status === 404) {
      throw new Error(`Model "${config.llm.model}" not found in LM Studio. Please load the correct model.`);
    } else {
      throw new Error(`Team analysis failed: ${error.message}`);
    }
  }
}

/**
 * Get appropriate system prompt based on ticket types and options
 */
function getSystemPrompt(tickets, options) {
  if (options.summaryMode) {
    return `You are a delivery lead assistant creating a comprehensive summary from multiple ticket analysis chunks.

CONTEXT: You are combining several smaller analyses into one cohesive delivery review summary.
PURPOSE: Create a unified analysis for client meeting preparation that preserves critical recurring issues.

CRITICAL REQUIREMENT: You MUST preserve specific recurring issues and examples from the chunks. Do NOT generalize away important patterns like specific recurring alarms, alerts, or system issues.

TASK: Analyze the provided text chunks and create a comprehensive summary that:
1. Combines insights from all chunks while preserving specific recurring issues
2. Identifies overarching patterns AND maintains specific examples (e.g., "Valmet-PROD-Airflow-ETL-monitoring-HeartbeatAlarm")
3. Preserves exact frequencies and specific alert names from individual chunks
4. Maintains delivery lead perspective with actionable specifics

ANALYSIS PRIORITIES:
1. PRESERVE SPECIFIC RECURRING ISSUES:
   - Maintain exact alert names and system identifiers
   - Keep specific frequencies from chunks (e.g., "15 Airflow heartbeat alarms")
   - Preserve examples of recurring problems that need systemic solutions

2. CROSS-CHUNK PATTERN RECOGNITION:
   - Look for the same specific issues appearing in multiple chunks
   - Combine frequencies when the same specific issue appears across chunks
   - Highlight systemic problems that span multiple chunks

3. MAINTAIN GRANULAR DETAILS:
   - Keep specific service names, alert types, and system identifiers
   - Preserve exact ticket examples and error messages
   - Maintain specific recommendations tied to recurring issues

RESPONSE FORMAT:
## 🚨 Service Desk Analysis Summary
[Brief overview preserving specific recurring issues and their impact]

## 🔍 Key Patterns Identified (with specific examples)
- **[Specific Alert/Issue Name]**: [Exact description with frequencies and examples]
  - Frequency: [Combined frequency across chunks]
  - Examples: [Specific ticket IDs or alert names]
  - Severity: [Business impact assessment]

## ⚡ Critical Items for Client Discussion
- [Specific recurring issue requiring immediate attention with exact names/frequencies]
- [Systemic problem with specific examples and business impact]

## 💡 Proactive Recommendations (prioritized)
1. **High Priority**: [Specific action for specific recurring issue]
2. **Medium Priority**: [Targeted improvement for identified pattern]
3. **Long-term**: [Strategic solution for systemic issues]

## 📊 Service Health Insights
- **Resolution Efficiency**: [Specific metrics from chunks]
- **Trend Analysis**: [Specific recurring patterns with examples]
- **Risk Assessment**: [Specific future risks based on recurring issues]

CRITICAL: Do NOT lose specific recurring issues like "Airflow heartbeat alarms" or "specific certificate expiration patterns" in favor of generic categories. The delivery lead needs specific, actionable details for client discussions.`;
  }

  // Determine if this is primarily service desk or client work based on ticket sources
  const hasServiceDeskTickets = tickets.some(t => t.sourceServer?.includes('Service Desk') || t.source === 'servicedesk');
  const hasClientTickets = tickets.some(t => t.sourceServer?.includes('Client') || t.source === 'primary');

  if (hasServiceDeskTickets && !hasClientTickets) {
    return `ROLE: You are a delivery lead assistant analyzing service desk tickets for Knowit-Valmet client reviews.

CONTEXT:
- Analyzing OPSVIOT project tickets from Knowit Service Desk
- Purpose: Monthly service desk review - identify recurring issues and prepare client discussion points
- Audience: Technical delivery lead preparing for client meetings and infrastructure discussions

ANALYSIS PRIORITIES:
1. PATTERN RECOGNITION & FREQUENCY ANALYSIS:
   - Group similar alerts by type (AWS, Kubernetes, certificates, access issues, monitoring)
   - Calculate exact frequencies and identify the most problematic areas
   - Highlight systemic vs one-off issues with specific examples
   - Identify timing patterns (peak hours, days, recurring schedules)

2. BUSINESS IMPACT & SEVERITY ASSESSMENT:
   - Assess criticality and client impact for each pattern type
   - Identify trends that affect service reliability and uptime
   - Categorize resolution complexity (quick fixes vs complex investigations)
   - Note any escalation patterns or unresolved issues

3. PROACTIVE RECOMMENDATIONS & ACTION ITEMS:
   - Suggest specific preventive measures with implementation priorities
   - Recommend infrastructure improvements with business justification
   - Identify concrete discussion points for Valmet technical team meetings
   - Propose monitoring enhancements to catch issues earlier

OUTPUT FORMAT:
## 🚨 Service Desk Analysis Summary
[Brief overview of ticket volume, types, and overall health trends]

## 🔍 Key Patterns Identified (with frequencies)
- **[Pattern Type]**: [Description, exact frequency, severity level, examples]
- **[Pattern Type]**: [Description, exact frequency, severity level, examples]

## ⚡ Critical Items for Client Discussion
- [Specific issue requiring immediate Valmet attention with business impact]
- [Systemic problem needing resolution with recommended timeline]

## 💡 Proactive Recommendations (prioritized)
1. **High Priority**: [Specific preventive action with expected impact]
2. **Medium Priority**: [Infrastructure improvement with business justification]
3. **Long-term**: [Strategic improvement suggestion]

## 📊 Service Health Insights
- **Resolution Efficiency**: [Quick vs complex issues with percentages]
- **Trend Analysis**: [Month-over-month patterns, improving/worsening areas]
- **Risk Assessment**: [Potential future issues based on current patterns]

Focus on actionable insights that help the delivery lead prepare for monthly client reviews and infrastructure planning discussions.`;
  }

  // Default to client work focused prompt
  return `ROLE: You are a delivery lead assistant analyzing team work for Valmet client delivery reviews.

CONTEXT:
- Analyzing Valmet Client Jira tickets (VIID project)
- Team: Cloud architects (trevainian3, tresmarohe, tresalotu)
- Purpose: Prepare client meeting with work accomplishments and progress

ANALYSIS PRIORITIES:
1. EPIC & FEATURE PROGRESS:
   - Analyze epic completion and business value delivered
   - Track feature development progress
   - Highlight major accomplishments

2. TEAM PERFORMANCE:
   - Individual contributor highlights
   - Time investment and effort distribution
   - Skill utilization and development

3. CLIENT VALUE DEMONSTRATION:
   - Tangible deliverables completed
   - Progress toward business objectives
   - Quality and timeline adherence

OUTPUT FORMAT:
## 👥 Team Delivery Summary
[Overview of team accomplishments and progress]

## 🎯 Epic Progress & Business Value
- **[Epic Name]**: [Progress status, business impact, completion %]

## ⭐ Key Accomplishments
- [Major deliverable or milestone]
- [Technical achievement or improvement]

## 📈 Team Performance Insights
- Total effort: [hours/days invested]
- Individual highlights: [who accomplished what]

## 🎤 Client Meeting Highlights
- [Ready-to-present achievement]
- [Progress milestone to showcase]

## 🔄 Next Steps & Commitments
- [Upcoming deliverables]
- [Timeline commitments]`;
}

/**
 * Handle large datasets by chunking - processes ALL tickets in manageable chunks
 */
async function summarizeInChunks(tickets, options, config) {
  const chunkSize = 50; // Optimal size for LM Studio processing
  const totalTickets = tickets.length;
  const totalChunks = Math.ceil(totalTickets / chunkSize);

  console.log(`🔄 Processing ${totalTickets} tickets in ${totalChunks} chunks of ${chunkSize} tickets each`);

  const chunkSummaries = [];

  // Process each chunk individually
  for (let i = 0; i < totalChunks; i++) {
    const startIndex = i * chunkSize;
    const endIndex = Math.min(startIndex + chunkSize, totalTickets);
    const chunkTickets = tickets.slice(startIndex, endIndex);

    console.log(`📝 Processing chunk ${i + 1}/${totalChunks} (tickets ${startIndex + 1}-${endIndex})`);

    try {
      // Process this chunk with chunk information
      const chunkOptions = {
        ...options,
        chunk: true,
        chunkIndex: i,
        totalChunks: totalChunks
      };

      const chunkSummary = await summarizeTickets(chunkTickets, chunkOptions, config);
      chunkSummaries.push({
        chunkIndex: i + 1,
        ticketRange: `${startIndex + 1}-${endIndex}`,
        ticketCount: chunkTickets.length,
        summary: chunkSummary
      });

      console.log(`✅ Completed chunk ${i + 1}/${totalChunks}`);

      // Small delay between chunks to be nice to LM Studio
      if (i < totalChunks - 1) {
        console.log(`⏳ Waiting 2 seconds before next chunk...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

    } catch (error) {
      console.error(`❌ Error processing chunk ${i + 1}:`, error.message);
      chunkSummaries.push({
        chunkIndex: i + 1,
        ticketRange: `${startIndex + 1}-${endIndex}`,
        ticketCount: chunkTickets.length,
        summary: `Error processing chunk ${i + 1}: ${error.message}`,
        error: true
      });
    }
  }

  // Now combine all chunk summaries into a comprehensive final summary
  console.log(`🔗 Combining ${chunkSummaries.length} chunk summaries into final analysis...`);

  const combinedSummaryText = chunkSummaries.map(chunk => {
    const errorPrefix = chunk.error ? '[ERROR] ' : '';
    return `## Analysis Chunk ${chunk.chunkIndex} (Tickets ${chunk.ticketRange}, ${chunk.ticketCount} tickets)

${errorPrefix}${chunk.summary}

---`;
  }).join('\n\n');

  // Add specific instructions for preserving recurring issues
  const preservationInstructions = `
CRITICAL INSTRUCTIONS FOR COMBINING CHUNKS:
- Look for the SAME specific alerts/issues appearing across multiple chunks
- Combine frequencies when the same specific issue appears (e.g., if Chunk 1 has 8 Airflow alerts and Chunk 2 has 7, report 15 total)
- Preserve exact alert names, service identifiers, and system names
- Maintain specific examples and ticket references
- Do NOT generalize specific recurring issues into broad categories
- Focus on actionable specifics that the delivery lead can discuss with clients

CHUNK SUMMARIES TO ANALYZE:
`;

  // Use the summary-of-summaries mode to create final comprehensive analysis
  const finalOptions = {
    ...options,
    summaryMode: true,
    textToSummarize: preservationInstructions + combinedSummaryText,
    totalTicketsProcessed: totalTickets,
    totalChunks: totalChunks
  };

  console.log(`🎯 Creating final comprehensive summary from ${totalChunks} chunks...`);
  const finalSummary = await summarizeTickets([], finalOptions, config);

  console.log(`✅ Chunked analysis completed: ${totalTickets} tickets processed in ${totalChunks} chunks`);
  return finalSummary;
}
