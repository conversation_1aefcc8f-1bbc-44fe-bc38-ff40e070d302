import { parseStringPromise } from 'xml2js';

/**
 * Clean HTML content from activity feed entries
 * @param {string} htmlContent - Raw HTML content
 * @returns {string} Clean text content
 */
function stripHtml(htmlContent) {
  if (!htmlContent) return '';

  let cleaned = htmlContent;

  // Decode HTML entities
  cleaned = cleaned
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&nbsp;/g, ' ');

  // Remove HTML tags
  cleaned = cleaned.replace(/<[^>]+>/g, '');

  // Collapse whitespace
  cleaned = cleaned.replace(/\s+/g, ' ').trim();

  return cleaned;
}

/**
 * Extract clean activity description from title
 * @param {string} title - Raw activity title
 * @returns {string} Clean, readable activity description
 */
function cleanActivityTitle(title) {
  if (!title) return '';

  // First strip HTML
  let cleaned = stripHtml(title);

  // Improve readability by adding spaces around ticket keys
  cleaned = cleaned.replace(/([A-Z]+-\d+)/g, ' $1 ');

  // Clean up extra spaces
  cleaned = cleaned.replace(/\s+/g, ' ').trim();

  return cleaned;
}

/**
 * Parse XML activity feed from Jira
 * @param {string} xmlData - Raw XML data from Jira activity feed
 * @param {string} memberName - Username for filtering
 * @param {string} dateFilter - Date filter for activities
 */
export async function parseActivityFeed(xmlData, memberName, dateFilter) {
  try {
    const result = await parseStringPromise(xmlData);
    const entries = result.feed?.entry || [];

    const activities = entries.map(entry => {
      // Extract basic information
      const rawTitle = entry.title?.[0]?._ || entry.title?.[0] || '';
      const published = entry.published?.[0] || '';
      const updated = entry.updated?.[0] || '';
      const id = entry.id?.[0] || '';

      // Clean the title for better readability
      const title = cleanActivityTitle(rawTitle);

      // Extract activity type from cleaned title
      let activityType = 'unknown';
      if (title.includes('commented on')) {
        activityType = 'comment';
      } else if (title.includes('updated')) {
        activityType = 'update';
      } else if (title.includes('created')) {
        activityType = 'create';
      } else if (title.includes('resolved')) {
        activityType = 'resolve';
      } else if (title.includes('assigned')) {
        activityType = 'assign';
      } else if (title.includes('logged work')) {
        activityType = 'worklog';
      }

      // Extract ticket key from title
      const ticketKeyMatch = title.match(/([A-Z]+-\d+)/);
      const ticketKey = ticketKeyMatch ? ticketKeyMatch[1] : null;

      // Extract link information from entry
      let ticketUrl = null;
      if (entry.link && Array.isArray(entry.link)) {
        // Look for the link with rel="alternate" which typically contains the ticket URL
        const alternateLink = entry.link.find(link =>
          link.$ && link.$.rel === 'alternate' && link.$.href
        );
        if (alternateLink) {
          ticketUrl = alternateLink.$.href;
        }
      }

      // Extract and clean summary or description
      const rawSummary = entry.summary?.[0]?._ || entry.summary?.[0] || '';
      const summary = stripHtml(rawSummary);

      // Format timestamp for better readability
      const formattedTime = published ? new Date(published).toLocaleString() : '';

      return {
        id,
        title,
        summary,
        published,
        updated,
        activityType,
        ticketKey,
        ticketUrl, // Add the extracted URL
        member: memberName,
        formattedTime,
        rawTitle, // Keep raw title for debugging if needed
        cleanTitle: title // Explicitly clean title for frontend
      };
    });

    // Filter activities by date if specified
    if (dateFilter) {
      const filterDate = new Date(dateFilter);
      return activities.filter(activity => {
        if (!activity.published) return false;
        const activityDate = new Date(activity.published);
        return activityDate >= filterDate;
      });
    }

    return activities;

  } catch (error) {
    console.error('Error parsing activity feed XML:', error.message);
    return [];
  }
}

/**
 * Analyze activity patterns from activity feed
 * @param {Array} activities - Array of activity objects
 */
export function analyzeActivityPatterns(activities) {
  if (!activities || activities.length === 0) {
    return {
      byType: {},
      perDay: {},
      mostActiveDay: null,
      totalActivities: 0
    };
  }

  // Group by activity type
  const byType = activities.reduce((acc, activity) => {
    acc[activity.activityType] = (acc[activity.activityType] || 0) + 1;
    return acc;
  }, {});

  // Group by day
  const perDay = activities.reduce((acc, activity) => {
    if (activity.published) {
      const day = activity.published.split('T')[0];
      acc[day] = (acc[day] || 0) + 1;
    }
    return acc;
  }, {});

  // Find most active day
  const mostActiveDay = Object.entries(perDay).reduce((max, [day, count]) => {
    return count > (max.count || 0) ? { day, count } : max;
  }, {});

  return {
    byType,
    perDay,
    mostActiveDay: mostActiveDay.day || null,
    totalActivities: activities.length
  };
}
