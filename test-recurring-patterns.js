#!/usr/bin/env node

/**
 * Test that recurring patterns like Airflow heartbeat alarms are preserved in chunked analysis
 */

import axios from 'axios';

const API_BASE = 'http://localhost:4000';

async function testRecurringPatterns() {
  try {
    console.log('🔍 Testing Recurring Pattern Preservation in Chunked Analysis');
    console.log('🎯 Focus: Ensuring specific recurring issues like Airflow heartbeat alarms are captured');
    console.log('=' + '='.repeat(80));
    
    console.log('\n1️⃣ Fetching Service Desk tickets...');
    const ticketsResponse = await axios.get(`${API_BASE}/api/tickets`, {
      params: {
        timeRange: '1month',
        server: 'servicedesk',
        maxResults: 100
      }
    });
    
    const tickets = ticketsResponse.data.tickets;
    console.log(`   ✅ Fetched ${tickets.length} tickets`);
    
    // Check for Airflow patterns in the raw data
    const airflowTickets = tickets.filter(ticket => 
      ticket.summary?.toLowerCase().includes('airflow') ||
      ticket.summary?.toLowerCase().includes('heartbeat')
    );
    
    console.log(`   🔍 Found ${airflowTickets.length} tickets with Airflow/heartbeat patterns in raw data`);
    
    if (airflowTickets.length > 0) {
      console.log('   📋 Sample Airflow tickets:');
      airflowTickets.slice(0, 3).forEach(ticket => {
        console.log(`      • ${ticket.key}: ${ticket.summary}`);
      });
    }
    
    console.log('\n2️⃣ Running Enhanced AI Analysis...');
    console.log('   🎯 Testing if specific recurring patterns are preserved');
    
    const startTime = Date.now();
    
    const analysisResponse = await axios.post(`${API_BASE}/api/analyze`, {
      tickets: tickets,
      options: {
        scenario: 'Recurring Pattern Test - Service Desk Analysis'
      }
    }, {
      timeout: 300000 // 5 minutes
    });
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    const analysis = analysisResponse.data.summary;
    
    console.log(`\n✅ Analysis completed in ${duration} seconds`);
    console.log(`   📝 Analysis length: ${analysis.length} characters`);
    
    // Test for specific recurring pattern preservation
    console.log('\n🔍 RECURRING PATTERN PRESERVATION TEST:');
    
    const patternTests = {
      'Contains "Airflow" or "heartbeat"': /airflow|heartbeat/i.test(analysis),
      'Has specific alert names': /valmet.*prod.*airflow|heartbeat.*alarm/i.test(analysis),
      'Contains specific frequencies': /\d+\s+(airflow|heartbeat)/i.test(analysis),
      'Mentions recurring/systemic issues': /recurring|systemic|multiple times|repeatedly/i.test(analysis),
      'Has specific examples': /OPSVIOT-\d+|valmet-prod/i.test(analysis),
      'Contains monitoring/ETL patterns': /monitoring|etl/i.test(analysis)
    };
    
    let passedTests = 0;
    Object.entries(patternTests).forEach(([test, passed]) => {
      console.log(`   ${passed ? '✅' : '❌'} ${test}`);
      if (passed) passedTests++;
    });
    
    const preservationScore = Math.round((passedTests / Object.keys(patternTests).length) * 100);
    console.log(`\n📊 Pattern Preservation Score: ${preservationScore}% (${passedTests}/${Object.keys(patternTests).length})`);
    
    // Show relevant sections of the analysis
    console.log('\n📄 ANALYSIS SECTIONS MENTIONING AIRFLOW/HEARTBEAT:');
    console.log('─'.repeat(80));
    
    const analysisLines = analysis.split('\n');
    const relevantLines = analysisLines.filter(line => 
      /airflow|heartbeat|monitoring.*alarm|etl/i.test(line)
    );
    
    if (relevantLines.length > 0) {
      relevantLines.forEach(line => console.log(line.trim()));
      console.log(`\n✅ Found ${relevantLines.length} lines mentioning relevant patterns`);
    } else {
      console.log('❌ NO LINES FOUND mentioning Airflow/heartbeat patterns');
      console.log('🚨 This indicates the chunking is losing specific recurring issues!');
    }
    
    console.log('─'.repeat(80));
    
    // Overall assessment
    if (preservationScore >= 70 && relevantLines.length > 0) {
      console.log('\n🎉 SUCCESS: Recurring patterns are being preserved!');
      console.log('   ✅ Specific issues like Airflow heartbeat alarms should be captured');
    } else if (preservationScore >= 50) {
      console.log('\n⚠️  PARTIAL: Some patterns preserved but needs improvement');
      console.log('   🔧 May need further prompt tuning for better specificity');
    } else {
      console.log('\n❌ FAILURE: Recurring patterns are being lost in chunking');
      console.log('   🚨 Critical issue - delivery lead needs specific recurring problems');
    }
    
    return {
      preservationScore,
      relevantLines: relevantLines.length,
      airflowTicketsFound: airflowTickets.length,
      analysis: analysis.substring(0, 1000) + '...'
    };
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    return { success: false, error: error.message };
  }
}

async function main() {
  console.log('🧪 RECURRING PATTERN PRESERVATION TEST\n');
  
  try {
    await axios.get(`${API_BASE}/api/health`);
    console.log('✅ Server is running\n');
  } catch (error) {
    console.log('❌ Server not running. Please start backend server.');
    process.exit(1);
  }
  
  const result = await testRecurringPatterns();
  
  if (result.success !== false) {
    console.log('\n🎯 TEST SUMMARY:');
    console.log(`   📊 Pattern preservation: ${result.preservationScore}%`);
    console.log(`   🔍 Relevant analysis lines: ${result.relevantLines}`);
    console.log(`   📋 Airflow tickets in data: ${result.airflowTicketsFound}`);
    
    if (result.preservationScore >= 70) {
      console.log('\n✅ The fix is working - specific recurring issues are preserved!');
    } else {
      console.log('\n⚠️  More tuning needed to preserve specific recurring patterns');
    }
  }
}

main().catch(console.error);
